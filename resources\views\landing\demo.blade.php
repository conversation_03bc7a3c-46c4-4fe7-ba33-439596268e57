<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Demo - SmartOCR.lk</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('landing.index') }}" class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-blue-600">SmartOCR.lk</h1>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ route('landing.index') }}" class="text-gray-700 hover:text-blue-600">Home</a>
                    <span class="text-blue-600 font-medium">Demo</span>
                    <a href="{{ route('demo2.index') }}" class="text-gray-700 hover:text-blue-600">Real OCR</a>
                    @auth
                        <a href="{{ route('dashboard') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Dashboard</a>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600">Login</a>
                        <a href="{{ route('register') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Get Started</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Demo Section -->
    <section class="py-12">
        <div class="max-w-4xl mx-auto px-4">
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Try SmartOCR Demo</h1>
                <p class="text-xl text-gray-600">Upload a sample document to see our OCR in action</p>
                <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mt-4">
                    <p><i class="fas fa-info-circle mr-2"></i>Demo mode: Limited to 1 document per day per IP address</p>
                </div>
            </div>

            <!-- Demo Upload Form -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <form id="demoForm" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-6">
                        <label for="document_type" class="block text-sm font-medium text-gray-700 mb-2">
                            Document Type
                        </label>
                        <select id="document_type" name="document_type" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select document type...</option>
                            @foreach($documentTypes as $type)
                                <option value="{{ $type->slug }}" data-supports-additional="{{ $type->supports_additional_file ? 'true' : 'false' }}">
                                    {{ $type->name }} ({{ $type->formatted_price }})
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-6">
                        <label for="file" class="block text-sm font-medium text-gray-700 mb-2">
                            Main Document
                        </label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition">
                            <input type="file" id="file" name="file" accept=".jpg,.jpeg,.png,.pdf" required class="hidden">
                            <div id="fileDropZone" class="cursor-pointer">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-lg text-gray-600 mb-2">Click to upload or drag and drop</p>
                                <p class="text-sm text-gray-500">JPG, PNG, PDF up to 10MB</p>
                            </div>
                            <div id="filePreview" class="hidden">
                                <div class="flex items-center justify-center space-x-2">
                                    <i class="fas fa-file text-blue-500"></i>
                                    <span id="fileName" class="text-gray-700"></span>
                                    <button type="button" id="removeFile" class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="additionalFileSection" class="mb-6 hidden">
                        <label for="additional_file" class="block text-sm font-medium text-gray-700 mb-2">
                            Additional File (Back side, etc.)
                        </label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition">
                            <input type="file" id="additional_file" name="additional_file" accept=".jpg,.jpeg,.png,.pdf" class="hidden">
                            <div id="additionalFileDropZone" class="cursor-pointer">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600 mb-1">Upload additional file (optional)</p>
                                <p class="text-sm text-gray-500">JPG, PNG, PDF up to 10MB</p>
                            </div>
                            <div id="additionalFilePreview" class="hidden">
                                <div class="flex items-center justify-center space-x-2">
                                    <i class="fas fa-file text-blue-500"></i>
                                    <span id="additionalFileName" class="text-gray-700"></span>
                                    <button type="button" id="removeAdditionalFile" class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" id="submitBtn" class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition disabled:bg-gray-400 disabled:cursor-not-allowed">
                        <span id="submitText">Process Document</span>
                        <i id="submitSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                    </button>
                </form>
            </div>

            <!-- Results Section -->
            <div id="resultsSection" class="mt-8 hidden">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Extraction Results</h3>
                    
                    <div id="processingStatus" class="mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
                            <span class="text-gray-700">Processing document...</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mt-3">
                            <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 45%"></div>
                        </div>
                    </div>

                    <div id="extractedData" class="hidden">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Extracted Information</h4>
                                <div id="dataFields" class="space-y-3">
                                    <!-- Dynamic content will be inserted here -->
                                </div>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Processing Details</h4>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Confidence Score:</span>
                                        <span id="confidenceScore" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Processing Time:</span>
                                        <span id="processingTime" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Document Type:</span>
                                        <span id="documentType" class="font-medium">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="errorMessage" class="hidden">
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <span id="errorText">An error occurred while processing the document.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="mt-12 text-center">
                <div class="bg-blue-50 rounded-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready for More?</h3>
                    <p class="text-gray-600 mb-6">Get unlimited access with higher accuracy and additional features</p>
                    @guest
                        <a href="{{ route('register') }}" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition">
                            Create Free Account
                        </a>
                    @else
                        <a href="{{ route('dashboard') }}" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition">
                            Go to Dashboard
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </section>

    <script>
        // Demo functionality will be added here
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('demoForm');
            const documentTypeSelect = document.getElementById('document_type');
            const additionalFileSection = document.getElementById('additionalFileSection');
            const fileInput = document.getElementById('file');
            const additionalFileInput = document.getElementById('additional_file');
            const resultsSection = document.getElementById('resultsSection');
            
            // Handle document type change
            documentTypeSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const supportsAdditional = selectedOption.dataset.supportsAdditional === 'true';
                
                if (supportsAdditional) {
                    additionalFileSection.classList.remove('hidden');
                } else {
                    additionalFileSection.classList.add('hidden');
                    additionalFileInput.value = '';
                }
            });

            // File upload handlers
            setupFileUpload('file', 'fileDropZone', 'filePreview', 'fileName', 'removeFile');
            setupFileUpload('additional_file', 'additionalFileDropZone', 'additionalFilePreview', 'additionalFileName', 'removeAdditionalFile');

            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                processDemo();
            });
        });

        function setupFileUpload(inputId, dropZoneId, previewId, fileNameId, removeId) {
            const input = document.getElementById(inputId);
            const dropZone = document.getElementById(dropZoneId);
            const preview = document.getElementById(previewId);
            const fileName = document.getElementById(fileNameId);
            const removeBtn = document.getElementById(removeId);

            dropZone.addEventListener('click', () => input.click());
            
            input.addEventListener('change', function() {
                if (this.files.length > 0) {
                    fileName.textContent = this.files[0].name;
                    dropZone.classList.add('hidden');
                    preview.classList.remove('hidden');
                }
            });

            removeBtn.addEventListener('click', function() {
                input.value = '';
                dropZone.classList.remove('hidden');
                preview.classList.add('hidden');
            });
        }

        function processDemo() {
            const form = document.getElementById('demoForm');
            const formData = new FormData(form);

            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const submitSpinner = document.getElementById('submitSpinner');
            const resultsSection = document.getElementById('resultsSection');
            const processingStatus = document.getElementById('processingStatus');
            const extractedData = document.getElementById('extractedData');
            const errorMessage = document.getElementById('errorMessage');

            // Show processing state
            submitBtn.disabled = true;
            submitText.textContent = 'Processing...';
            submitSpinner.classList.remove('hidden');
            resultsSection.classList.remove('hidden');
            processingStatus.classList.remove('hidden');
            extractedData.classList.add('hidden');
            errorMessage.classList.add('hidden');

            // Make actual API call to demo endpoint
            fetch('{{ route("demo.process") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                // Hide processing
                processingStatus.classList.add('hidden');

                if (data.success) {
                    // Show results
                    extractedData.classList.remove('hidden');
                    showDemoResults(data.data);
                } else {
                    // Show error
                    errorMessage.classList.remove('hidden');
                    document.getElementById('errorText').textContent = data.error.message;
                }

                // Reset button
                submitBtn.disabled = false;
                submitText.textContent = 'Process Another Document';
                submitSpinner.classList.add('hidden');
            })
            .catch(error => {
                console.error('Error:', error);

                // Hide processing, show error
                processingStatus.classList.add('hidden');
                errorMessage.classList.remove('hidden');
                document.getElementById('errorText').textContent = 'An error occurred while processing the document.';

                // Reset button
                submitBtn.disabled = false;
                submitText.textContent = 'Try Again';
                submitSpinner.classList.add('hidden');
            });
        }

        function showDemoResults(responseData) {
            const dataFields = document.getElementById('dataFields');
            const confidenceScore = document.getElementById('confidenceScore');
            const processingTime = document.getElementById('processingTime');
            const documentTypeSpan = document.getElementById('documentType');

            // Use actual response data
            const data = responseData.result;

            // Populate fields
            dataFields.innerHTML = '';
            Object.entries(data).forEach(([key, value]) => {
                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'flex justify-between py-2 border-b border-gray-200';

                // Handle array values
                const displayValue = Array.isArray(value) ? value.join(', ') : value;

                fieldDiv.innerHTML = `
                    <span class="text-gray-600">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span>
                    <span class="font-medium">${displayValue}</span>
                `;
                dataFields.appendChild(fieldDiv);
            });

            // Set processing details from response
            confidenceScore.textContent = (responseData.confidence_score * 100).toFixed(1) + '%';
            processingTime.textContent = (responseData.processing_time_ms / 1000).toFixed(2) + 's';
            documentTypeSpan.textContent = responseData.document_type.replace('_', ' ').toUpperCase();

            // Show demo notice if applicable
            if (responseData.demo_mode) {
                const demoNotice = document.createElement('div');
                demoNotice.className = 'mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md';
                demoNotice.innerHTML = `
                    <div class="flex">
                        <i class="fas fa-info-circle text-yellow-400 mr-2 mt-0.5"></i>
                        <p class="text-sm text-yellow-700">${responseData.message}</p>
                    </div>
                `;
                dataFields.appendChild(demoNotice);
            }
        }
    </script>
</body>
</html>
