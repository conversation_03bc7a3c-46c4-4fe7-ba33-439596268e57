<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_types', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('slug', 50)->unique();
            $table->text('description')->nullable();
            $table->integer('credit_cost')->default(1);
            $table->integer('max_file_size_mb')->default(10);
            $table->json('allowed_extensions');
            $table->boolean('supports_additional_file')->default(false);
            $table->integer('processing_time_estimate_seconds')->default(30);
            $table->boolean('is_active')->default(true);
            $table->json('validation_rules')->nullable();
            $table->timestamps();

            $table->index('slug');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_types');
    }
};
