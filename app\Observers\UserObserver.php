<?php

namespace App\Observers;

use App\Models\User;
use App\Services\PlanService;
use Illuminate\Support\Facades\Log;

class UserObserver
{
    protected $planService;

    public function __construct(PlanService $planService)
    {
        $this->planService = $planService;
    }

    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        try {
            // Skip admin user
            if ($user->email === '<EMAIL>') {
                return;
            }

            // Create personal team if user doesn't have one
            if (!$user->currentTeam) {
                $teamName = explode('@', $user->email)[0] . "'s Team";
                $team = $user->ownedTeams()->create([
                    'name' => $teamName,
                    'personal_team' => true,
                ]);
                $user->current_team_id = $team->id;
                $user->save();
            }

            // Assign free plan to new user
            $this->planService->assignFreePlanToUser($user);

            Log::info('Free plan assigned to new user', [
                'user_id' => $user->id,
                'email' => $user->email,
                'team_id' => $user->current_team_id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to assign free plan to new user', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        //
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        //
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        //
    }
}
