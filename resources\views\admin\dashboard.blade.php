@extends('layouts.dashboard')

@section('title', 'Admin Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
                    <p class="mt-1 text-sm text-gray-600">System overview and management</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.users') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        <i class="fas fa-users mr-2"></i>
                        Manage Users
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-users text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['total_users']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Documents -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-file-alt text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Documents</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['total_documents']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documents Today -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-calendar-day text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Documents Today</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['documents_today']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-user-check text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Users (30d)</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['active_users']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Documents -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Documents</h3>
                    <a href="{{ route('admin.documents') }}" class="text-sm text-blue-600 hover:text-blue-500">View all</a>
                </div>
                
                @if($recentDocuments->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentDocuments as $document)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    @if($document->status === 'completed')
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-green-600 text-sm"></i>
                                        </div>
                                    @elseif($document->status === 'failed')
                                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-times text-red-600 text-sm"></i>
                                        </div>
                                    @elseif($document->status === 'processing')
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-spinner fa-spin text-blue-600 text-sm"></i>
                                        </div>
                                    @else
                                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-clock text-yellow-600 text-sm"></i>
                                        </div>
                                    @endif
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ Str::limit($document->original_filename, 25) }}</p>
                                    <p class="text-xs text-gray-500">
                                        {{ $document->user->name }} • {{ $document->documentType->name }} • {{ $document->created_at->diffForHumans() }}
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($document->status === 'completed') bg-green-100 text-green-800
                                    @elseif($document->status === 'failed') bg-red-100 text-red-800
                                    @elseif($document->status === 'processing') bg-blue-100 text-blue-800
                                    @else bg-yellow-100 text-yellow-800 @endif">
                                    {{ ucfirst($document->status) }}
                                </span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-6">
                        <i class="fas fa-file-alt text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No recent documents</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Documents by Type -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Documents by Type</h3>
                
                @if($documentsByType->count() > 0)
                    <div class="space-y-4">
                        @foreach($documentsByType as $typeData)
                            @php
                                $percentage = $stats['total_documents'] > 0 ? ($typeData->count / $stats['total_documents']) * 100 : 0;
                            @endphp
                            <div>
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">{{ $typeData->name }}</span>
                                    <span class="text-sm text-gray-500">{{ number_format($typeData->count) }} ({{ number_format($percentage, 1) }}%)</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-6">
                        <i class="fas fa-chart-pie text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No documents processed yet</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{{ route('admin.users') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-blue-900">Manage Users</p>
                        <p class="text-xs text-blue-700">View and manage user accounts</p>
                    </div>
                </a>
                
                <a href="{{ route('admin.documents') }}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition">
                    <div class="flex-shrink-0">
                        <i class="fas fa-file-alt text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-900">View Documents</p>
                        <p class="text-xs text-green-700">Browse all processed documents</p>
                    </div>
                </a>
                
                <a href="{{ route('admin.analytics') }}" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-bar text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-purple-900">Analytics</p>
                        <p class="text-xs text-purple-700">View system analytics</p>
                    </div>
                </a>
                
                <a href="{{ route('admin.settings') }}" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition">
                    <div class="flex-shrink-0">
                        <i class="fas fa-cog text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-orange-900">Settings</p>
                        <p class="text-xs text-orange-700">Configure system settings</p>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">System Status</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Database Status -->
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Database</p>
                        <p class="text-xs text-gray-500">Connected</p>
                    </div>
                </div>

                <!-- Queue Status -->
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Queue System</p>
                        <p class="text-xs text-gray-500">Running</p>
                    </div>
                </div>

                <!-- Storage Status -->
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">File Storage</p>
                        <p class="text-xs text-gray-500">Available</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
