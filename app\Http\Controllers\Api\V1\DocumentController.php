<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Models\DocumentType;
use App\Services\DocumentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * @OA\Info(
 *     title="SmartOCR.lk API",
 *     version="1.0.0",
 *     description="API for SmartOCR.lk - Intelligent document processing for Sri Lankan businesses",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="SmartOCR Support"
 *     ),
 *     @OA\License(
 *         name="MIT",
 *         url="https://opensource.org/licenses/MIT"
 *     )
 * )
 *
 * @OA\Server(
 *     url="http://localhost/Multiblity/2025/smartocr/public/api/v1",
 *     description="Local Development Server"
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT"
 * )
 */
class DocumentController extends Controller
{
    protected $documentService;

    public function __construct(DocumentService $documentService)
    {
        $this->documentService = $documentService;
    }

    /**
     * @OA\Post(
     *     path="/documents/upload",
     *     summary="Upload a document for OCR processing",
     *     description="Upload a document file for OCR processing. Supports NIC, driving license, vehicle registration, land deed, and invoice.",
     *     operationId="uploadDocument",
     *     tags={"Documents"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="file",
     *                     type="string",
     *                     format="binary",
     *                     description="Document file to upload (JPG, PNG, PDF)"
     *                 ),
     *                 @OA\Property(
     *                     property="document_type",
     *                     type="string",
     *                     description="Type of document (nic, driving_license, vehicle_registration, land_deed, invoice)",
     *                     example="nic"
     *                 ),
     *                 @OA\Property(
     *                     property="additional_file",
     *                     type="string",
     *                     format="binary",
     *                     description="Additional file (e.g., back of NIC) - optional"
     *                 ),
     *                 @OA\Property(
     *                     property="webhook_url",
     *                     type="string",
     *                     format="url",
     *                     description="URL to receive webhook notification when processing is complete - optional",
     *                     example="https://your-app.com/webhook"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Document uploaded successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="document_id", type="string", format="uuid", example="550e8400-e29b-41d4-a716-************"),
     *                 @OA\Property(property="status", type="string", example="pending"),
     *                 @OA\Property(property="credits_used", type="integer", example=1),
     *                 @OA\Property(property="estimated_processing_time", type="integer", example=30),
     *                 @OA\Property(property="expires_at", type="string", format="date-time", example="2024-01-16T10:30:00.000000Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid input",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(
     *                 property="error",
     *                 type="object",
     *                 @OA\Property(property="code", type="string", example="VALIDATION_ERROR"),
     *                 @OA\Property(property="message", type="string", example="The file field is required.")
     *             )
     *         )
     *     )
     * )
     */
    public function upload(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
                'document_type' => 'required|string|exists:document_types,slug',
                'additional_file' => 'nullable|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
                'webhook_url' => 'nullable|url',
            ]);

            $user = $request->user();

            $document = $this->documentService->uploadDocument(
                $user,
                $request->file('file'),
                $request->document_type,
                $request->file('additional_file'),
                $request->webhook_url
            );

            // Start processing
            $this->documentService->processDocument($document);

            return response()->json([
                'success' => true,
                'data' => [
                    'document_id' => $document->uuid,
                    'status' => $document->status,
                    'credits_used' => $document->credits_used,
                    'estimated_processing_time' => 30,
                    'expires_at' => $document->expires_at,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API document upload failed', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'PROCESSING_FAILED',
                    'message' => $e->getMessage(),
                ]
            ], 400);
        }
    }
}
