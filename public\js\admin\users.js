// Admin Users Management JavaScript

// CSRF Token Setup
const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

// Utility Functions
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function makeRequest(url, method = 'GET', data = null) {
    const options = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json',
        },
    };
    
    if (data) {
        options.body = JSON.stringify(data);
    }
    
    return fetch(url, options);
}

// Credits Management
function editCredits(userId) {
    document.getElementById('credits_user_id').value = userId;
    document.getElementById('creditsModal').classList.remove('hidden');
}

function hideCreditsModal() {
    document.getElementById('creditsModal').classList.add('hidden');
    document.getElementById('creditsForm').reset();
}

document.getElementById('creditsForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const userId = formData.get('user_id');
    
    const data = {
        credits: parseInt(formData.get('credits')),
        action: formData.get('action'),
        reason: formData.get('reason'),
    };
    
    try {
        const response = await makeRequest(`/admin/users/${userId}/credits`, 'PATCH', data);
        const result = await response.json();
        
        if (result.success) {
            showNotification(result.message, 'success');
            hideCreditsModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message, 'error');
        }
    } catch (error) {
        showNotification('An error occurred while updating credits', 'error');
        console.error('Error:', error);
    }
});

// Plan Management
function managePlan(userId) {
    document.getElementById('plan_user_id').value = userId;
    document.getElementById('planModal').classList.remove('hidden');
    updatePlanSummary();
}

function hidePlanModal() {
    document.getElementById('planModal').classList.add('hidden');
    document.getElementById('planForm').reset();
    document.getElementById('plan_summary').classList.add('hidden');
}

function updatePlanSummary() {
    const planSelect = document.getElementById('plan_slug');
    const durationSelect = document.getElementById('duration_months');
    const summary = document.getElementById('plan_summary');
    
    if (planSelect.value && durationSelect.value) {
        const selectedOption = planSelect.options[planSelect.selectedIndex];
        const planName = selectedOption.text;
        const duration = durationSelect.value;
        
        document.getElementById('summary_plan_name').textContent = planName;
        document.getElementById('summary_duration').textContent = `${duration} month(s)`;
        
        if (planSelect.value === 'free') {
            document.getElementById('summary_expires').textContent = 'Never';
        } else {
            const expiryDate = new Date();
            expiryDate.setMonth(expiryDate.getMonth() + parseInt(duration));
            document.getElementById('summary_expires').textContent = expiryDate.toLocaleDateString();
        }
        
        summary.classList.remove('hidden');
    } else {
        summary.classList.add('hidden');
    }
}

document.getElementById('plan_slug')?.addEventListener('change', updatePlanSummary);
document.getElementById('duration_months')?.addEventListener('change', updatePlanSummary);

document.getElementById('planForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const userId = formData.get('user_id');
    
    const data = {
        plan_slug: formData.get('plan_slug'),
        duration_months: parseInt(formData.get('duration_months')),
        reason: formData.get('reason'),
    };
    
    try {
        const response = await makeRequest(`/admin/users/${userId}/plan`, 'PATCH', data);
        const result = await response.json();
        
        if (result.success) {
            showNotification(result.message, 'success');
            hidePlanModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message, 'error');
        }
    } catch (error) {
        showNotification('An error occurred while updating plan', 'error');
        console.error('Error:', error);
    }
});

// Payment Recording
function recordPayment(userId) {
    document.getElementById('payment_user_id').value = userId;
    document.getElementById('paymentModal').classList.remove('hidden');
}

function hidePaymentModal() {
    document.getElementById('paymentModal').classList.add('hidden');
    document.getElementById('paymentForm').reset();
}

document.getElementById('paymentForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const userId = formData.get('user_id');
    
    const data = {
        amount: parseFloat(formData.get('amount')),
        currency: formData.get('currency'),
        plan_slug: formData.get('plan_slug'),
        duration_months: parseInt(formData.get('duration_months')),
        payment_method: formData.get('payment_method'),
        reference: formData.get('reference'),
        notes: formData.get('notes'),
    };
    
    try {
        const response = await makeRequest(`/admin/users/${userId}/payment`, 'POST', data);
        const result = await response.json();
        
        if (result.success) {
            showNotification(result.message, 'success');
            hidePaymentModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message, 'error');
        }
    } catch (error) {
        showNotification('An error occurred while recording payment', 'error');
        console.error('Error:', error);
    }
});

// User Actions
function viewUser(userId) {
    window.location.href = `/admin/users/${userId}`;
}

function viewUsage(userId) {
    // Open usage logs modal or navigate to usage page
    window.open(`/admin/users/${userId}/usage`, '_blank');
}

function suspendUser(userId) {
    const reason = prompt('Please provide a reason for suspension:');
    if (reason) {
        if (confirm('Are you sure you want to suspend this user? This will deactivate their plan and revoke API tokens.')) {
            suspendUserAccount(userId, reason);
        }
    }
}

async function suspendUserAccount(userId, reason) {
    try {
        const response = await makeRequest(`/admin/users/${userId}/suspend`, 'POST', { reason });
        const result = await response.json();
        
        if (result.success) {
            showNotification(result.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(result.message, 'error');
        }
    } catch (error) {
        showNotification('An error occurred while suspending user', 'error');
        console.error('Error:', error);
    }
}

// Export Functions
function exportUsers() {
    const format = prompt('Export format (csv or excel):', 'csv');
    if (format && ['csv', 'excel'].includes(format.toLowerCase())) {
        const url = new URL('/admin/users/export', window.location.origin);
        url.searchParams.append('format', format.toLowerCase());
        
        // Add current filters
        const urlParams = new URLSearchParams(window.location.search);
        for (const [key, value] of urlParams) {
            url.searchParams.append(`filters[${key}]`, value);
        }
        
        window.open(url.toString(), '_blank');
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Add any initialization code here
    console.log('Admin Users Management loaded');
});
