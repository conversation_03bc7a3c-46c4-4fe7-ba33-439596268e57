# SmartOCR.lk Troubleshooting Guide

## Common Issues and Solutions

### 1. Application Not Loading

#### Symptoms
- White screen or 500 error
- "Application in maintenance mode" message
- Database connection errors

#### Solutions

**Check Application Status**
```bash
# Check if application is in maintenance mode
php artisan down --show

# Bring application back up
php artisan up
```

**Database Connection Issues**
```bash
# Test database connection
php artisan tinker
>>> DB::connection()->getPdo();

# Check database configuration
cat .env | grep DB_
```

**Clear Application Cache**
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Regenerate optimized files
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

**File Permissions (Linux)**
```bash
# Set correct permissions
sudo chown -R www-data:www-data /var/www/smartocr
sudo chmod -R 755 /var/www/smartocr/storage
sudo chmod -R 755 /var/www/smartocr/bootstrap/cache
```

### 2. Demo Rate Limiting Not Working

#### Symptoms
- Users can submit multiple demo requests
- No entries in `demo_requests` table
- Rate limit errors not showing

#### Solutions

**Check Database Table**
```sql
-- Verify table exists
SHOW TABLES LIKE 'demo_requests';

-- Check table structure
DESCRIBE demo_requests;

-- Check recent entries
SELECT * FROM demo_requests ORDER BY created_at DESC LIMIT 10;
```

**Verify Migration**
```bash
# Check migration status
php artisan migrate:status

# Run missing migrations
php artisan migrate
```

**Test Rate Limiting**
```bash
# Test demo endpoint
curl -X POST http://localhost/Multiblity/2025/smartocr/demo2/process \
  -F "file=@test.jpg" \
  -F "document_type=nic"

# Check database entry
mysql -u root -p smartocr -e "SELECT * FROM demo_requests WHERE ip_address='127.0.0.1';"
```

### 3. External OCR API Issues

#### Symptoms
- Demo2 always returns simulated results
- "API connection failed" errors
- Timeout errors

#### Solutions

**Test API Connectivity**
```bash
# Test direct API call
curl -X POST https://doc.ornsys.com/apinic.php \
  -d "name=test.jpg&image=$(base64 -w 0 test.jpg)"

# Check DNS resolution
nslookup doc.ornsys.com

# Test with different timeout
curl --connect-timeout 30 --max-time 60 https://doc.ornsys.com/
```

**Check Application Logs**
```bash
# View Laravel logs
tail -f storage/logs/laravel.log

# Check for OCR-related errors
grep -i "ocr\|external" storage/logs/laravel.log
```

**Verify SSL/TLS**
```bash
# Test SSL connection
openssl s_client -connect doc.ornsys.com:443 -servername doc.ornsys.com

# Check certificate validity
curl -I https://doc.ornsys.com/
```

### 4. File Upload Issues

#### Symptoms
- "File too large" errors
- Upload fails silently
- Invalid file type errors

#### Solutions

**Check PHP Configuration**
```bash
# Check upload limits
php -i | grep -E "(upload_max_filesize|post_max_size|max_execution_time)"

# Recommended settings for .htaccess or php.ini:
upload_max_filesize = 10M
post_max_size = 12M
max_execution_time = 300
memory_limit = 256M
```

**Verify File Permissions**
```bash
# Check storage directory
ls -la storage/app/

# Create missing directories
mkdir -p storage/app/documents
chmod 755 storage/app/documents
```

**Test File Upload**
```php
// Test in tinker
php artisan tinker
>>> $file = new \Illuminate\Http\UploadedFile('/path/to/test.jpg', 'test.jpg', 'image/jpeg', null, true);
>>> $file->isValid();
```

### 5. Authentication Issues

#### Symptoms
- Login redirects to login page
- "Unauthenticated" API responses
- Session expires immediately

#### Solutions

**Check Session Configuration**
```bash
# Verify session driver
grep SESSION_DRIVER .env

# Clear sessions
php artisan session:table  # if using database sessions
rm storage/framework/sessions/*  # if using file sessions
```

**API Token Issues**
```bash
# Check Sanctum configuration
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

# Clear API tokens
php artisan tinker
>>> \Laravel\Sanctum\PersonalAccessToken::truncate();
```

**Cookie/CSRF Issues**
```bash
# Check CSRF configuration
grep APP_URL .env

# Ensure APP_URL matches your domain
APP_URL=http://localhost/Multiblity/2025/smartocr
```

### 6. Admin Panel Access Issues

#### Symptoms
- 403 Forbidden errors
- Admin routes not working
- User not recognized as admin

#### Solutions

**Verify Admin User**
```sql
-- Check admin user exists
SELECT * FROM users WHERE email = '<EMAIL>';

-- Update user to admin if needed
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';
```

**Check Admin Middleware**
```bash
# Verify routes
php artisan route:list | grep admin

# Test admin check
php artisan tinker
>>> $user = \App\Models\User::where('email', '<EMAIL>')->first();
>>> $user->isAdmin();
```

### 7. Database Issues

#### Symptoms
- Migration errors
- Foreign key constraint errors
- Table doesn't exist errors

#### Solutions

**Reset Database**
```bash
# Fresh migration (WARNING: Deletes all data)
php artisan migrate:fresh --seed

# Rollback and re-run specific migration
php artisan migrate:rollback --step=1
php artisan migrate
```

**Fix Foreign Key Issues**
```sql
-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS=0;

-- Re-enable after fixing
SET FOREIGN_KEY_CHECKS=1;
```

**Check Database Encoding**
```sql
-- Check database charset
SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'smartocr';

-- Should be utf8mb4_unicode_ci
```

### 8. Performance Issues

#### Symptoms
- Slow page loads
- Timeout errors
- High memory usage

#### Solutions

**Optimize Database**
```sql
-- Add missing indexes
CREATE INDEX idx_demo_requests_ip_date ON demo_requests(ip_address, created_at);
CREATE INDEX idx_documents_user_status ON documents(user_id, status);

-- Analyze tables
ANALYZE TABLE demo_requests, documents, users;
```

**Enable Caching**
```bash
# Enable Redis caching
composer require predis/predis

# Update .env
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

**Optimize Application**
```bash
# Enable OPcache in php.ini
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000

# Optimize Composer autoloader
composer install --optimize-autoloader --no-dev
```

### 9. Queue System Issues

#### Symptoms
- Documents stuck in "processing" status
- Queue jobs not running
- Failed jobs accumulating

#### Solutions

**Check Queue Status**
```bash
# View failed jobs
php artisan queue:failed

# Restart queue workers
php artisan queue:restart

# Process jobs manually
php artisan queue:work --once
```

**Monitor Queue Workers**
```bash
# Check if workers are running
ps aux | grep "queue:work"

# Start queue worker
php artisan queue:work redis --sleep=3 --tries=3
```

**Clear Failed Jobs**
```bash
# Retry all failed jobs
php artisan queue:retry all

# Clear failed jobs
php artisan queue:flush
```

### 10. Email Issues

#### Symptoms
- Contact form emails not sending
- Password reset emails not received
- SMTP errors

#### Solutions

**Test Email Configuration**
```bash
# Test email in tinker
php artisan tinker
>>> Mail::raw('Test email', function($msg) { $msg->to('<EMAIL>')->subject('Test'); });
```

**Check Email Settings**
```bash
# Verify MAIL configuration in .env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
```

## Debugging Tools

### Laravel Telescope (Development)
```bash
# Install Telescope for debugging
composer require laravel/telescope --dev
php artisan telescope:install
php artisan migrate
```

### Log Analysis
```bash
# Monitor logs in real-time
tail -f storage/logs/laravel.log

# Search for specific errors
grep -i "error\|exception\|failed" storage/logs/laravel.log

# Check web server logs
tail -f /var/log/nginx/error.log  # Nginx
tail -f /var/log/apache2/error.log  # Apache
```

### Database Debugging
```sql
-- Enable query logging
SET GLOBAL general_log = 'ON';
SET GLOBAL general_log_file = '/var/log/mysql/queries.log';

-- Check slow queries
SHOW PROCESSLIST;
SHOW FULL PROCESSLIST;
```

## Environment-Specific Issues

### Windows XAMPP Issues

**Path Issues**
```bash
# Use forward slashes in paths
APP_URL=http://localhost/Multiblity/2025/smartocr

# Check file permissions
# Ensure XAMPP has write access to storage/ and bootstrap/cache/
```

**Database Connection**
```bash
# Check MySQL service is running
# Start MySQL from XAMPP Control Panel

# Verify database exists
mysql -u root -e "SHOW DATABASES LIKE 'smartocr';"
```

### Linux Production Issues

**Service Management**
```bash
# Check services
systemctl status nginx
systemctl status mysql
systemctl status redis

# Restart services
sudo systemctl restart nginx
sudo systemctl restart mysql
```

**Log Locations**
```bash
# Application logs
/var/www/smartocr/storage/logs/

# System logs
/var/log/nginx/
/var/log/mysql/
/var/log/syslog
```

## Emergency Recovery

### Backup Restoration
```bash
# Restore database backup
mysql -u root -p smartocr < backup_20240115.sql

# Restore file backup
tar -xzf storage_backup_20240115.tar.gz -C /var/www/smartocr/
```

### Quick Fix Commands
```bash
# Emergency reset (development only)
php artisan migrate:fresh --seed
php artisan cache:clear
php artisan config:clear
composer install
npm run build

# Fix permissions
sudo chown -R www-data:www-data /var/www/smartocr
sudo chmod -R 755 /var/www/smartocr/storage
```

## Getting Help

### Log Information to Collect
1. Laravel logs: `storage/logs/laravel.log`
2. Web server error logs
3. Database error logs
4. System information: `php -v`, `mysql --version`
5. Environment file (without sensitive data)

### Support Channels
- **Email:** <EMAIL>
- **Documentation:** Check README.md and docs/
- **GitHub Issues:** Create detailed issue reports

### Creating Bug Reports
Include:
1. Steps to reproduce
2. Expected vs actual behavior
3. Error messages and logs
4. Environment details
5. Screenshots if applicable
