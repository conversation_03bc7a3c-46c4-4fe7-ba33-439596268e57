<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Plan;
use App\Services\PlanService;
use App\Services\AdminUserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserController extends Controller
{
    protected $planService;
    protected $adminUserService;

    public function __construct(PlanService $planService, AdminUserService $adminUserService)
    {
        $this->planService = $planService;
        $this->adminUserService = $adminUserService;
    }

    /**
     * Display a listing of users/companies
     */
    public function index(Request $request)
    {
        $query = User::with(['currentPlan.plan', 'teams', 'ownedTeams', 'tokens', 'documents'])
                    ->where('email', '!=', '<EMAIL>');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by plan
        if ($request->filled('plan')) {
            $query->whereHas('currentPlan.plan', function($q) use ($request) {
                $q->where('slug', $request->plan);
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->whereHas('currentPlan', function($q) {
                        $q->where('is_active', true);
                    });
                    break;
                case 'expired':
                    $query->whereHas('currentPlan', function($q) {
                        $q->where('expires_at', '<', now());
                    });
                    break;
                case 'no_plan':
                    $query->doesntHave('currentPlan');
                    break;
            }
        }

        $users = $query->paginate(20);
        $plans = Plan::all();

        return view('admin.users.index', compact('users', 'plans'));
    }

    /**
     * Display the specified user/company details
     */
    public function show(User $user)
    {
        $user->load([
            'currentPlan.plan',
            'teams',
            'ownedTeams.members',
            'tokens',
            'documents.documentType',
            'documents.result'
        ]);

        $usageStats = $this->adminUserService->getUserUsageStats($user);
        $paymentHistory = $this->adminUserService->getPaymentHistory($user);

        return view('admin.users.show', compact('user', 'usageStats', 'paymentHistory'));
    }

    /**
     * Update user credits
     */
    public function updateCredits(Request $request, User $user)
    {
        $request->validate([
            'credits' => 'required|integer|min:0|max:999999',
            'action' => 'required|in:set,add,subtract',
            'reason' => 'nullable|string|max:255',
        ]);

        try {
            $oldCredits = $user->remaining_credits;

            switch ($request->action) {
                case 'set':
                    $user->update(['remaining_credits' => $request->credits]);
                    break;
                case 'add':
                    $user->increment('remaining_credits', $request->credits);
                    break;
                case 'subtract':
                    $user->decrement('remaining_credits', $request->credits);
                    break;
            }

            // Log the credit change
            $this->adminUserService->logCreditChange($user, $oldCredits, $user->remaining_credits, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Credits updated successfully.',
                'new_credits' => $user->remaining_credits,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update credits: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update user plan
     */
    public function updatePlan(Request $request, User $user)
    {
        $request->validate([
            'plan_slug' => 'required|string|exists:plans,slug',
            'duration_months' => 'required|integer|min:1|max:24',
            'reason' => 'nullable|string|max:255',
        ]);

        try {
            $plan = Plan::where('slug', $request->plan_slug)->first();
            $expiresAt = $request->plan_slug === 'free' ? null : now()->addMonths($request->duration_months);

            $this->planService->upgradeUserPlan($user, $request->plan_slug, $expiresAt);

            // Log the plan change
            $this->adminUserService->logPlanChange($user, $plan, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Plan updated successfully.',
                'new_plan' => $plan->name,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update plan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Record offline payment
     */
    public function recordPayment(Request $request, User $user)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|in:LKR,USD',
            'plan_slug' => 'required|string|exists:plans,slug',
            'duration_months' => 'required|integer|min:1|max:24',
            'payment_method' => 'required|string|max:100',
            'reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            // Record the payment
            $payment = $this->adminUserService->recordOfflinePayment($user, [
                'amount' => $request->amount,
                'currency' => $request->currency,
                'plan_slug' => $request->plan_slug,
                'duration_months' => $request->duration_months,
                'payment_method' => $request->payment_method,
                'reference' => $request->reference,
                'notes' => $request->notes,
            ]);

            // Update the plan
            $plan = Plan::where('slug', $request->plan_slug)->first();
            $expiresAt = $request->plan_slug === 'free' ? null : now()->addMonths($request->duration_months);
            $this->planService->upgradeUserPlan($user, $request->plan_slug, $expiresAt);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Payment recorded and plan updated successfully.',
                'payment_id' => $payment->id,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to record payment: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Suspend user account
     */
    public function suspend(Request $request, User $user)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $this->adminUserService->suspendUser($user, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'User suspended successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to suspend user: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Activate user account
     */
    public function activate(Request $request, User $user)
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $this->adminUserService->activateUser($user, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'User activated successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to activate user: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user usage logs
     */
    public function usageLogs(User $user)
    {
        $logs = $this->adminUserService->getUserUsageLogs($user);

        return response()->json([
            'success' => true,
            'logs' => $logs,
        ]);
    }

    /**
     * Export user data
     */
    public function export(Request $request)
    {
        $request->validate([
            'format' => 'required|in:csv,excel',
            'filters' => 'nullable|array',
        ]);

        try {
            $export = $this->adminUserService->exportUsers($request->format, $request->filters ?? []);

            return $export;
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export data: ' . $e->getMessage(),
            ], 500);
        }
    }
}
