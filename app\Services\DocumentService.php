<?php

namespace App\Services;

use App\Models\Document;
use App\Models\DocumentType;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DocumentService
{
    protected $ocrService;

    public function __construct(OcrService $ocrService)
    {
        $this->ocrService = $ocrService;
    }

    public function uploadDocument(
        User $user,
        UploadedFile $file,
        string $documentTypeSlug,
        ?UploadedFile $additionalFile = null,
        ?string $webhookUrl = null
    ): Document {
        return DB::transaction(function () use ($user, $file, $documentTypeSlug, $additionalFile, $webhookUrl) {
            $documentType = DocumentType::where('slug', $documentTypeSlug)->firstOrFail();
            
            // Check if user has enough credits
            if (!$user->hasCredits($documentType->credit_cost)) {
                throw new \Exception('Insufficient credits');
            }

            // Validate file
            $this->validateFile($file, $documentType);
            
            // Upload main file
            $filePath = $this->ocrService->uploadFile($file);
            
            // Upload additional file if provided
            $additionalFilePath = null;
            if ($additionalFile && $documentType->supports_additional_file) {
                $this->validateFile($additionalFile, $documentType);
                $additionalFilePath = $this->ocrService->uploadFile($additionalFile);
            }

            // Create document record
            $document = Document::create([
                'user_id' => $user->id,
                'team_id' => $user->currentTeam?->id,
                'document_type_id' => $documentType->id,
                'file_path' => $filePath,
                'additional_file_path' => $additionalFilePath,
                'original_filename' => $file->getClientOriginalName(),
                'file_size_bytes' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'webhook_url' => $webhookUrl,
                'expires_at' => now()->addHours(config('app.document_retention_hours', 24)),
            ]);

            // Use credits
            $user->useCredits($documentType->credit_cost);
            
            // Update document with credits used
            $document->update(['credits_used' => $documentType->credit_cost]);

            return $document;
        });
    }

    public function processDocument(Document $document): void
    {
        try {
            // Mark as processing
            $document->update([
                'status' => 'processing',
                'processing_started_at' => now(),
            ]);

            // Process immediately with real OCR service
            $this->ocrService->processDocument($document);

        } catch (\Exception $e) {
            // Mark as failed if processing fails
            $document->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'processing_completed_at' => now(),
            ]);

            Log::error('Document processing failed', [
                'document_id' => $document->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function getDocumentResult(Document $document): ?array
    {
        if (!$document->isCompleted()) {
            return null;
        }

        $result = $document->result;
        if (!$result) {
            return null;
        }

        return [
            'document_id' => $document->uuid,
            'status' => $document->status,
            'document_type' => $document->documentType->slug,
            'confidence_score' => $document->confidence_score,
            'processing_time_ms' => $result->processing_time_ms,
            'result' => $result->extracted_data,
            'created_at' => $document->created_at->toISOString(),
            'completed_at' => $document->processing_completed_at?->toISOString(),
        ];
    }

    public function getUserDocuments(User $user, array $filters = []): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = $user->documents()->with(['documentType', 'result']);

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['document_type'])) {
            $query->whereHas('documentType', function ($q) use ($filters) {
                $q->where('slug', $filters['document_type']);
            });
        }

        if (isset($filters['from_date'])) {
            $query->where('created_at', '>=', $filters['from_date']);
        }

        if (isset($filters['to_date'])) {
            $query->where('created_at', '<=', $filters['to_date']);
        }

        return $query->orderBy('created_at', 'desc')
                    ->paginate($filters['per_page'] ?? 20);
    }

    public function deleteExpiredDocuments(): int
    {
        $expiredDocuments = Document::expired()->get();
        $count = 0;

        foreach ($expiredDocuments as $document) {
            try {
                // Delete files
                if ($document->file_path) {
                    $this->ocrService->deleteFile($document->file_path);
                }
                
                if ($document->additional_file_path) {
                    $this->ocrService->deleteFile($document->additional_file_path);
                }

                // Soft delete document
                $document->delete();
                $count++;
            } catch (\Exception $e) {
                Log::error('Failed to delete expired document', [
                    'document_id' => $document->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $count;
    }

    private function validateFile(UploadedFile $file, DocumentType $documentType): void
    {
        // Check file size
        $maxSizeBytes = $documentType->max_file_size_mb * 1024 * 1024;
        if ($file->getSize() > $maxSizeBytes) {
            throw new \Exception("File size exceeds maximum allowed size of {$documentType->max_file_size_mb}MB");
        }

        // Check file extension
        $allowedExtensions = $documentType->allowed_extensions;
        $fileExtension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($fileExtension, $allowedExtensions)) {
            throw new \Exception("File type not allowed. Allowed types: " . implode(', ', $allowedExtensions));
        }

        // Check if file is valid
        if (!$file->isValid()) {
            throw new \Exception("Invalid file upload");
        }
    }
}
