@extends('layouts.dashboard')

@section('title', 'Document Details')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ $document->original_filename }}</h1>
                    <p class="mt-1 text-sm text-gray-600">{{ $document->documentType->name }} • Uploaded {{ $document->created_at->diffForHumans() }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        @if($document->status === 'completed') bg-green-100 text-green-800
                        @elseif($document->status === 'failed') bg-red-100 text-red-800
                        @elseif($document->status === 'processing') bg-blue-100 text-blue-800
                        @elseif($document->status === 'manual_review') bg-yellow-100 text-yellow-800
                        @else bg-gray-100 text-gray-800 @endif">
                        @if($document->status === 'processing')
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                        @elseif($document->status === 'completed')
                            <i class="fas fa-check mr-2"></i>
                        @elseif($document->status === 'failed')
                            <i class="fas fa-times mr-2"></i>
                        @elseif($document->status === 'manual_review')
                            <i class="fas fa-eye mr-2"></i>
                        @else
                            <i class="fas fa-clock mr-2"></i>
                        @endif
                        {{ ucfirst(str_replace('_', ' ', $document->status)) }}
                    </span>
                    <a href="{{ route('dashboard.documents') }}" class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Documents
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Document Information -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Document Information</h3>
                    
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Document ID</dt>
                            <dd class="text-sm text-gray-900 font-mono">{{ $document->uuid }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Type</dt>
                            <dd class="text-sm text-gray-900">{{ $document->documentType->name }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">File Size</dt>
                            <dd class="text-sm text-gray-900">{{ $document->formatted_file_size }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Credits Used</dt>
                            <dd class="text-sm text-gray-900">{{ $document->credits_used }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Uploaded</dt>
                            <dd class="text-sm text-gray-900">{{ $document->created_at->format('M j, Y g:i A') }}</dd>
                        </div>
                        
                        @if($document->processing_started_at)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Processing Started</dt>
                            <dd class="text-sm text-gray-900">{{ $document->processing_started_at->format('M j, Y g:i A') }}</dd>
                        </div>
                        @endif
                        
                        @if($document->processing_completed_at)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Processing Completed</dt>
                            <dd class="text-sm text-gray-900">{{ $document->processing_completed_at->format('M j, Y g:i A') }}</dd>
                        </div>
                        @endif
                        
                        @if($document->confidence_score)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Confidence Score</dt>
                            <dd class="text-sm text-gray-900">{{ number_format($document->confidence_score * 100, 1) }}%</dd>
                        </div>
                        @endif
                        
                        @if($document->expires_at)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Expires</dt>
                            <dd class="text-sm text-gray-900">{{ $document->expires_at->format('M j, Y g:i A') }}</dd>
                        </div>
                        @endif
                    </dl>
                </div>
            </div>
        </div>

        <!-- Processing Results -->
        <div class="lg:col-span-2">
            @if($document->status === 'processing' || $document->status === 'pending')
                <!-- Processing Status -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="text-center py-8">
                            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
                                <i class="fas fa-cog fa-spin text-2xl text-blue-600"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Processing Document</h3>
                            <p class="text-gray-600 mb-4">Your document is being processed. This usually takes 30-60 seconds.</p>
                            
                            <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                                <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 45%"></div>
                            </div>
                            
                            <button onclick="window.location.reload()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                                <i class="fas fa-sync-alt mr-2"></i>
                                Refresh Status
                            </button>
                        </div>
                    </div>
                </div>
            @elseif($document->status === 'completed' && $document->result)
                <!-- Extraction Results -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Extraction Results</h3>
                            <div class="flex space-x-2">
                                <button onclick="copyResults()" class="text-blue-600 hover:text-blue-500 text-sm">
                                    <i class="fas fa-copy mr-1"></i>
                                    Copy JSON
                                </button>
                                <button onclick="downloadResults()" class="text-blue-600 hover:text-blue-500 text-sm">
                                    <i class="fas fa-download mr-1"></i>
                                    Download
                                </button>
                            </div>
                        </div>
                        
                        @if($document->result->extracted_data)
                            <div class="space-y-4">
                                @foreach($document->result->extracted_data as $key => $value)
                                    <div class="border-b border-gray-200 pb-3">
                                        <dt class="text-sm font-medium text-gray-500 mb-1">{{ ucfirst(str_replace('_', ' ', $key)) }}</dt>
                                        <dd class="text-sm text-gray-900">
                                            @if(is_array($value))
                                                {{ implode(', ', $value) }}
                                            @else
                                                {{ $value }}
                                            @endif
                                        </dd>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                        
                        <!-- Processing Details -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Processing Details</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                @if($document->result->processing_time_ms)
                                <div>
                                    <span class="text-gray-500">Processing Time:</span>
                                    <span class="text-gray-900 ml-2">{{ $document->result->processing_time }}</span>
                                </div>
                                @endif
                                
                                @if($document->confidence_score)
                                <div>
                                    <span class="text-gray-500">Confidence:</span>
                                    <span class="text-gray-900 ml-2">{{ number_format($document->confidence_score * 100, 1) }}%</span>
                                </div>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Raw JSON Data (Hidden by default) -->
                        <div class="mt-6">
                            <button onclick="toggleRawData()" class="text-sm text-gray-600 hover:text-gray-900">
                                <i class="fas fa-code mr-1"></i>
                                <span id="rawDataToggle">Show Raw JSON</span>
                            </button>
                            <div id="rawDataContainer" class="hidden mt-3">
                                <pre id="rawJsonData" class="bg-gray-100 p-4 rounded-md text-xs overflow-x-auto">{{ json_encode($document->result->extracted_data, JSON_PRETTY_PRINT) }}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            @elseif($document->status === 'failed')
                <!-- Error Status -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="text-center py-8">
                            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                                <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Processing Failed</h3>
                            <p class="text-gray-600 mb-4">There was an error processing your document.</p>
                            
                            @if($document->error_message)
                                <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                                    <p class="text-sm text-red-700">{{ $document->error_message }}</p>
                                </div>
                            @endif
                            
                            <div class="space-x-3">
                                <a href="{{ route('dashboard.upload') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                                    Try Again
                                </a>
                                <a href="{{ route('landing.contact') }}" class="text-gray-600 hover:text-gray-900">
                                    Contact Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @elseif($document->status === 'manual_review')
                <!-- Manual Review Status -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="text-center py-8">
                            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
                                <i class="fas fa-eye text-2xl text-yellow-600"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Manual Review Required</h3>
                            <p class="text-gray-600 mb-4">Your document requires manual review for accuracy.</p>
                            
                            @if($document->manual_review_reason)
                                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                                    <p class="text-sm text-yellow-700">{{ $document->manual_review_reason }}</p>
                                </div>
                            @endif
                            
                            <p class="text-sm text-gray-500">Our team will review your document and provide results within 24 hours.</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
function toggleRawData() {
    const container = document.getElementById('rawDataContainer');
    const toggle = document.getElementById('rawDataToggle');
    
    if (container.classList.contains('hidden')) {
        container.classList.remove('hidden');
        toggle.textContent = 'Hide Raw JSON';
    } else {
        container.classList.add('hidden');
        toggle.textContent = 'Show Raw JSON';
    }
}

function copyResults() {
    const rawData = document.getElementById('rawJsonData');
    if (rawData) {
        navigator.clipboard.writeText(rawData.textContent).then(() => {
            // Show success message
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
            button.classList.add('text-green-600');
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('text-green-600');
            }, 2000);
        });
    }
}

function downloadResults() {
    const data = @json($document->result->extracted_data ?? []);
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '{{ $document->original_filename }}_results.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Auto-refresh for processing documents
@if($document->status === 'processing' || $document->status === 'pending')
setTimeout(() => {
    window.location.reload();
}, 10000); // Refresh every 10 seconds
@endif
</script>
@endpush
@endsection
