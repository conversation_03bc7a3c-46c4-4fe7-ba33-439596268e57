<?php

return [
    'nic' => [
        'name' => 'National Identity Card',
        'fields' => [
            'nic_number' => [
                'label' => 'NIC Number',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:20',
                'export_order' => 1
            ],
            'full_name' => [
                'label' => 'Full Name',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:255',
                'export_order' => 2
            ],
            'name_with_initials' => [
                'label' => 'Name with Initials',
                'type' => 'string',
                'required' => false,
                'validation' => 'nullable|string|max:255',
                'export_order' => 3
            ],
            'date_of_birth' => [
                'label' => 'Date of Birth',
                'type' => 'date',
                'required' => true,
                'validation' => 'required|date',
                'export_order' => 4
            ],
            'gender' => [
                'label' => 'Gender',
                'type' => 'string',
                'required' => false,
                'validation' => 'nullable|in:Male,Female',
                'export_order' => 5
            ],
            'address' => [
                'label' => 'Address',
                'type' => 'text',
                'required' => false,
                'validation' => 'nullable|string',
                'export_order' => 6
            ]
        ]
    ],
    
    'driving_license' => [
        'name' => 'Driving License',
        'fields' => [
            'license_number' => [
                'label' => 'License Number',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:20',
                'export_order' => 1
            ],
            'full_name' => [
                'label' => 'Full Name',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:255',
                'export_order' => 2
            ],
            'date_of_birth' => [
                'label' => 'Date of Birth',
                'type' => 'date',
                'required' => true,
                'validation' => 'required|date',
                'export_order' => 3
            ],
            'issue_date' => [
                'label' => 'Issue Date',
                'type' => 'date',
                'required' => false,
                'validation' => 'nullable|date',
                'export_order' => 4
            ],
            'expiry_date' => [
                'label' => 'Expiry Date',
                'type' => 'date',
                'required' => true,
                'validation' => 'required|date',
                'export_order' => 5
            ],
            'vehicle_classes' => [
                'label' => 'Vehicle Classes',
                'type' => 'string',
                'required' => false,
                'validation' => 'nullable|string',
                'export_order' => 6
            ],
            'address' => [
                'label' => 'Address',
                'type' => 'text',
                'required' => false,
                'validation' => 'nullable|string',
                'export_order' => 7
            ]
        ]
    ],
    
    'vehicle_registration' => [
        'name' => 'Vehicle Registration',
        'fields' => [
            'registration_number' => [
                'label' => 'Registration Number',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:20',
                'export_order' => 1
            ],
            'vehicle_make' => [
                'label' => 'Vehicle Make',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:100',
                'export_order' => 2
            ],
            'vehicle_model' => [
                'label' => 'Vehicle Model',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:100',
                'export_order' => 3
            ],
            'year_of_manufacture' => [
                'label' => 'Year of Manufacture',
                'type' => 'integer',
                'required' => false,
                'validation' => 'nullable|integer|min:1900|max:' . date('Y'),
                'export_order' => 4
            ],
            'engine_number' => [
                'label' => 'Engine Number',
                'type' => 'string',
                'required' => false,
                'validation' => 'nullable|string|max:50',
                'export_order' => 5
            ],
            'chassis_number' => [
                'label' => 'Chassis Number',
                'type' => 'string',
                'required' => false,
                'validation' => 'nullable|string|max:50',
                'export_order' => 6
            ],
            'owner_name' => [
                'label' => 'Owner Name',
                'type' => 'string',
                'required' => false,
                'validation' => 'nullable|string|max:255',
                'export_order' => 7
            ]
        ]
    ],
    
    'land_deed' => [
        'name' => 'Land Deed',
        'fields' => [
            'deed_number' => [
                'label' => 'Deed Number',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:50',
                'export_order' => 1
            ],
            'property_address' => [
                'label' => 'Property Address',
                'type' => 'text',
                'required' => true,
                'validation' => 'required|string',
                'export_order' => 2
            ],
            'owner_name' => [
                'label' => 'Owner Name',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:255',
                'export_order' => 3
            ],
            'land_extent' => [
                'label' => 'Land Extent',
                'type' => 'string',
                'required' => false,
                'validation' => 'nullable|string|max:100',
                'export_order' => 4
            ],
            'survey_plan_number' => [
                'label' => 'Survey Plan Number',
                'type' => 'string',
                'required' => false,
                'validation' => 'nullable|string|max:50',
                'export_order' => 5
            ],
            'registration_date' => [
                'label' => 'Registration Date',
                'type' => 'date',
                'required' => false,
                'validation' => 'nullable|date',
                'export_order' => 6
            ]
        ]
    ],
    
    'invoice' => [
        'name' => 'Invoice',
        'fields' => [
            'invoice_number' => [
                'label' => 'Invoice Number',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:50',
                'export_order' => 1
            ],
            'vendor_name' => [
                'label' => 'Vendor Name',
                'type' => 'string',
                'required' => true,
                'validation' => 'required|string|max:255',
                'export_order' => 2
            ],
            'vendor_address' => [
                'label' => 'Vendor Address',
                'type' => 'text',
                'required' => false,
                'validation' => 'nullable|string',
                'export_order' => 3
            ],
            'invoice_date' => [
                'label' => 'Invoice Date',
                'type' => 'date',
                'required' => false,
                'validation' => 'nullable|date',
                'export_order' => 4
            ],
            'due_date' => [
                'label' => 'Due Date',
                'type' => 'date',
                'required' => false,
                'validation' => 'nullable|date',
                'export_order' => 5
            ],
            'total_amount' => [
                'label' => 'Total Amount',
                'type' => 'decimal',
                'required' => true,
                'validation' => 'required|numeric|min:0',
                'export_order' => 6
            ],
            'currency' => [
                'label' => 'Currency',
                'type' => 'string',
                'required' => false,
                'validation' => 'nullable|string|max:10',
                'export_order' => 7
            ],
            'tax_amount' => [
                'label' => 'Tax Amount',
                'type' => 'decimal',
                'required' => false,
                'validation' => 'nullable|numeric|min:0',
                'export_order' => 8
            ]
        ]
    ]
];
