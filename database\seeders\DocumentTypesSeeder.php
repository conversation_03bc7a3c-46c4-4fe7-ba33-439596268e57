<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DocumentTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $documentTypes = [
            [
                'name' => 'National Identity Card',
                'slug' => 'nic',
                'description' => 'Sri Lankan National Identity Card (front and back)',
                'credit_cost' => 1,
                'supports_additional_file' => true,
                'max_file_size_mb' => 10,
                'allowed_extensions' => json_encode(['jpg', 'jpeg', 'png', 'pdf']),
                'validation_rules' => json_encode([
                    'required_fields' => ['nic_number', 'full_name', 'date_of_birth']
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Driving License',
                'slug' => 'driving_license',
                'description' => 'Sri Lankan Driving License',
                'credit_cost' => 1,
                'supports_additional_file' => true,
                'max_file_size_mb' => 10,
                'allowed_extensions' => json_encode(['jpg', 'jpeg', 'png', 'pdf']),
                'validation_rules' => json_encode([
                    'required_fields' => ['license_number', 'full_name', 'expiry_date']
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Vehicle Registration',
                'slug' => 'vehicle_registration',
                'description' => 'Vehicle Registration Certificate',
                'credit_cost' => 2,
                'supports_additional_file' => false,
                'max_file_size_mb' => 10,
                'allowed_extensions' => json_encode(['jpg', 'jpeg', 'png', 'pdf']),
                'validation_rules' => json_encode([
                    'required_fields' => ['registration_number', 'vehicle_make', 'vehicle_model']
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Land Deed',
                'slug' => 'land_deed',
                'description' => 'Property Land Deed Document',
                'credit_cost' => 3,
                'supports_additional_file' => false,
                'validation_rules' => json_encode([
                    'required_fields' => ['deed_number', 'property_address', 'owner_name']
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Invoice',
                'slug' => 'invoice',
                'description' => 'Commercial Invoice Document',
                'credit_cost' => 1,
                'supports_additional_file' => false,
                'validation_rules' => json_encode([
                    'required_fields' => ['invoice_number', 'total_amount', 'vendor_name']
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('document_types')->insert($documentTypes);
    }
}
