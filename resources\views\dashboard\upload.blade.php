@extends('layouts.dashboard')

@section('title', 'Upload Document')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Upload Document</h1>
                    <p class="mt-1 text-sm text-gray-600">Process your documents with AI-powered OCR</p>
                </div>
                <div class="text-right">
                    @if(auth()->user()->currentPlan)
                        <p class="text-sm text-gray-600">Credits Remaining</p>
                        <p class="text-2xl font-bold text-blue-600">{{ auth()->user()->remaining_credits }}</p>
                    @else
                        <p class="text-sm text-red-600">No active plan</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form id="uploadForm" action="{{ route('dashboard.upload.process') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                @csrf
                
                <!-- Document Type Selection -->
                <div>
                    <label for="document_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Document Type *
                    </label>
                    <select id="document_type" name="document_type" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('document_type') border-red-500 @enderror">
                        <option value="">Select document type...</option>
                        @foreach($documentTypes as $type)
                            <option value="{{ $type->slug }}" 
                                    data-credits="{{ $type->credit_cost }}"
                                    data-supports-additional="{{ $type->supports_additional_file ? 'true' : 'false' }}"
                                    data-max-size="{{ $type->max_file_size_mb }}"
                                    data-extensions="{{ implode(',', $type->allowed_extensions) }}"
                                    {{ old('document_type') == $type->slug ? 'selected' : '' }}>
                                {{ $type->name }} ({{ $type->formatted_price }})
                            </option>
                        @endforeach
                    </select>
                    @error('document_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    
                    <div id="documentTypeInfo" class="mt-2 hidden">
                        <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-700">
                                        <span id="typeDescription"></span>
                                    </p>
                                    <div class="mt-2 text-xs text-blue-600">
                                        <span>Credits required: <strong id="creditsRequired">-</strong></span> |
                                        <span>Max file size: <strong id="maxFileSize">-</strong></span> |
                                        <span>Allowed formats: <strong id="allowedFormats">-</strong></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main File Upload -->
                <div>
                    <label for="file" class="block text-sm font-medium text-gray-700 mb-2">
                        Main Document *
                    </label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition @error('file') border-red-500 @enderror">
                        <input type="file" id="file" name="file" accept=".jpg,.jpeg,.png,.pdf" required class="hidden">
                        <div id="fileDropZone" class="cursor-pointer">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-lg text-gray-600 mb-2">Click to upload or drag and drop</p>
                            <p class="text-sm text-gray-500">JPG, PNG, PDF up to <span id="maxSizeDisplay">10MB</span></p>
                        </div>
                        <div id="filePreview" class="hidden">
                            <div class="flex items-center justify-center space-x-3">
                                <i class="fas fa-file text-blue-500 text-2xl"></i>
                                <div class="text-left">
                                    <p id="fileName" class="text-gray-700 font-medium"></p>
                                    <p id="fileSize" class="text-sm text-gray-500"></p>
                                </div>
                                <button type="button" id="removeFile" class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    @error('file')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Additional File Upload -->
                <div id="additionalFileSection" class="hidden">
                    <label for="additional_file" class="block text-sm font-medium text-gray-700 mb-2">
                        Additional File (Back side, etc.)
                    </label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition">
                        <input type="file" id="additional_file" name="additional_file" accept=".jpg,.jpeg,.png,.pdf" class="hidden">
                        <div id="additionalFileDropZone" class="cursor-pointer">
                            <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                            <p class="text-gray-600 mb-1">Upload additional file (optional)</p>
                            <p class="text-sm text-gray-500">JPG, PNG, PDF up to <span id="additionalMaxSizeDisplay">10MB</span></p>
                        </div>
                        <div id="additionalFilePreview" class="hidden">
                            <div class="flex items-center justify-center space-x-3">
                                <i class="fas fa-file text-blue-500 text-xl"></i>
                                <div class="text-left">
                                    <p id="additionalFileName" class="text-gray-700 font-medium"></p>
                                    <p id="additionalFileSize" class="text-sm text-gray-500"></p>
                                </div>
                                <button type="button" id="removeAdditionalFile" class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    @error('additional_file')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Webhook URL -->
                <div>
                    <label for="webhook_url" class="block text-sm font-medium text-gray-700 mb-2">
                        Webhook URL (Optional)
                    </label>
                    <input type="url" id="webhook_url" name="webhook_url" value="{{ old('webhook_url') }}"
                           placeholder="https://your-app.com/webhook"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('webhook_url') border-red-500 @enderror">
                    <p class="mt-1 text-sm text-gray-500">We'll send a POST request to this URL when processing is complete</p>
                    @error('webhook_url')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Submit Button -->
                <div class="flex items-center justify-between pt-4">
                    <div class="text-sm text-gray-600">
                        <span id="processingInfo" class="hidden">
                            Processing will use <strong id="creditsToUse">-</strong> credit(s)
                        </span>
                    </div>
                    <button type="submit" id="submitBtn" 
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition disabled:bg-gray-400 disabled:cursor-not-allowed">
                        <span id="submitText">Process Document</span>
                        <i id="submitSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Recent Uploads -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Uploads</h3>
            
            @if(auth()->user()->documents()->orderBy('created_at', 'desc')->limit(5)->count() > 0)
                <div class="space-y-3">
                    @foreach(auth()->user()->documents()->with(['documentType', 'result'])->orderBy('created_at', 'desc')->limit(5)->get() as $document)
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                @if($document->status === 'completed')
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-green-600 text-sm"></i>
                                    </div>
                                @elseif($document->status === 'failed')
                                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-times text-red-600 text-sm"></i>
                                    </div>
                                @elseif($document->status === 'processing')
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-spinner fa-spin text-blue-600 text-sm"></i>
                                    </div>
                                @else
                                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-clock text-yellow-600 text-sm"></i>
                                    </div>
                                @endif
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $document->original_filename }}</p>
                                <p class="text-xs text-gray-500">{{ $document->documentType->name }} • {{ $document->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($document->status === 'completed') bg-green-100 text-green-800
                                @elseif($document->status === 'failed') bg-red-100 text-red-800
                                @elseif($document->status === 'processing') bg-blue-100 text-blue-800
                                @else bg-yellow-100 text-yellow-800 @endif">
                                {{ ucfirst($document->status) }}
                            </span>
                            @if($document->status === 'completed')
                                <a href="{{ route('dashboard.documents.show', $document->uuid) }}" class="text-blue-600 hover:text-blue-500 text-sm">
                                    View Results
                                </a>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <div class="mt-4 text-center">
                    <a href="{{ route('dashboard.documents') }}" class="text-blue-600 hover:text-blue-500 text-sm">
                        View all documents →
                    </a>
                </div>
            @else
                <div class="text-center py-6">
                    <i class="fas fa-file-alt text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500">No documents uploaded yet</p>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('uploadForm');
    const documentTypeSelect = document.getElementById('document_type');
    const additionalFileSection = document.getElementById('additionalFileSection');
    const documentTypeInfo = document.getElementById('documentTypeInfo');
    
    // Document type data
    const documentTypes = @json($documentTypes->keyBy('slug'));
    
    // Handle document type change
    documentTypeSelect.addEventListener('change', function() {
        const selectedSlug = this.value;
        
        if (selectedSlug && documentTypes[selectedSlug]) {
            const type = documentTypes[selectedSlug];
            
            // Show/hide additional file section
            if (type.supports_additional_file) {
                additionalFileSection.classList.remove('hidden');
            } else {
                additionalFileSection.classList.add('hidden');
                document.getElementById('additional_file').value = '';
                resetFilePreview('additional');
            }
            
            // Update document type info
            document.getElementById('typeDescription').textContent = type.description;
            document.getElementById('creditsRequired').textContent = type.credit_cost;
            document.getElementById('maxFileSize').textContent = type.max_file_size_mb + 'MB';
            document.getElementById('allowedFormats').textContent = type.allowed_extensions.join(', ').toUpperCase();
            document.getElementById('maxSizeDisplay').textContent = type.max_file_size_mb + 'MB';
            document.getElementById('additionalMaxSizeDisplay').textContent = type.max_file_size_mb + 'MB';
            document.getElementById('creditsToUse').textContent = type.credit_cost;
            
            documentTypeInfo.classList.remove('hidden');
            document.getElementById('processingInfo').classList.remove('hidden');
        } else {
            additionalFileSection.classList.add('hidden');
            documentTypeInfo.classList.add('hidden');
            document.getElementById('processingInfo').classList.add('hidden');
        }
    });

    // File upload handlers
    setupFileUpload('file', 'fileDropZone', 'filePreview', 'fileName', 'fileSize', 'removeFile');
    setupFileUpload('additional_file', 'additionalFileDropZone', 'additionalFilePreview', 'additionalFileName', 'additionalFileSize', 'removeAdditionalFile');

    // Form submission
    form.addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const submitSpinner = document.getElementById('submitSpinner');
        
        submitBtn.disabled = true;
        submitText.textContent = 'Processing...';
        submitSpinner.classList.remove('hidden');
    });
});

function setupFileUpload(inputId, dropZoneId, previewId, fileNameId, fileSizeId, removeId) {
    const input = document.getElementById(inputId);
    const dropZone = document.getElementById(dropZoneId);
    const preview = document.getElementById(previewId);
    const fileName = document.getElementById(fileNameId);
    const fileSize = document.getElementById(fileSizeId);
    const removeBtn = document.getElementById(removeId);

    // Click to upload
    dropZone.addEventListener('click', () => input.click());
    
    // Drag and drop
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('border-blue-400', 'bg-blue-50');
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('border-blue-400', 'bg-blue-50');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('border-blue-400', 'bg-blue-50');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            input.files = files;
            handleFileSelect(files[0]);
        }
    });
    
    // File input change
    input.addEventListener('change', function() {
        if (this.files.length > 0) {
            handleFileSelect(this.files[0]);
        }
    });

    // Remove file
    removeBtn.addEventListener('click', function() {
        input.value = '';
        resetFilePreview(inputId.replace('_file', ''));
    });
    
    function handleFileSelect(file) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        dropZone.classList.add('hidden');
        preview.classList.remove('hidden');
    }
}

function resetFilePreview(type) {
    const dropZone = document.getElementById(type === 'additional' ? 'additionalFileDropZone' : 'fileDropZone');
    const preview = document.getElementById(type === 'additional' ? 'additionalFilePreview' : 'filePreview');
    
    dropZone.classList.remove('hidden');
    preview.classList.add('hidden');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>
@endpush
@endsection
