<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartOCR.lk - Intelligent Document Processing for Sri Lanka</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-blue-600">SmartOCR.lk</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="#features" class="text-gray-700 hover:text-blue-600">Features</a>
                    <a href="#pricing" class="text-gray-700 hover:text-blue-600">Pricing</a>
                    <a href="{{ route('demo2.index') }}" class="text-gray-700 hover:text-blue-600">Demo</a>
                    <a href="{{ route('landing.contact') }}" class="text-gray-700 hover:text-blue-600">Contact</a>
                    @auth
                        @if(auth()->user()->email === '<EMAIL>')
                            <a href="{{ route('admin.dashboard') }}" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">Admin</a>
                        @endif
                        <a href="{{ route('dashboard') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Dashboard</a>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600">Login</a>
                        <a href="{{ route('register') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Get Started</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div class="max-w-7xl mx-auto px-4 py-20">
            <div class="text-center">
                <h1 class="text-5xl font-bold mb-6">Intelligent OCR for Sri Lankan Documents</h1>
                <p class="text-xl mb-8 max-w-3xl mx-auto">
                    Extract data from NICs, driving licenses, vehicle registrations, land deeds, and invoices with AI-powered accuracy. 
                    Built specifically for Sri Lankan businesses.
                </p>
                <div class="space-x-4">
                    @guest
                        <a href="{{ route('register') }}" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition">
                            Start Free Trial
                        </a>
                    @endguest
                    <a href="{{ route('demo2.index') }}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition">
                        Try Demo
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Powerful Features</h2>
                <p class="text-xl text-gray-600">Everything you need for document processing</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center p-6">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-alt text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Multiple Document Types</h3>
                    <p class="text-gray-600">Support for NICs, driving licenses, vehicle registrations, land deeds, and invoices</p>
                </div>
                
                <div class="text-center p-6">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-bolt text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Fast Processing</h3>
                    <p class="text-gray-600">Get results in seconds with our optimized OCR engine</p>
                </div>
                
                <div class="text-center p-6">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shield-alt text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Secure & Private</h3>
                    <p class="text-gray-600">Documents auto-deleted after 24 hours, compliant with Sri Lankan data protection laws</p>
                </div>
                
                <div class="text-center p-6">
                    <div class="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-users text-2xl text-orange-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Team Collaboration</h3>
                    <p class="text-gray-600">Work together with role-based access and team management</p>
                </div>
                
                <div class="text-center p-6">
                    <div class="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-code text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">API Access</h3>
                    <p class="text-gray-600">Integrate with your existing systems using our RESTful API</p>
                </div>
                
                <div class="text-center p-6">
                    <div class="bg-indigo-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-chart-bar text-2xl text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Analytics</h3>
                    <p class="text-gray-600">Track usage, success rates, and performance metrics</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Supported Documents -->
    <section class="bg-gray-100 py-20">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Supported Documents</h2>
                <p class="text-xl text-gray-600">Specialized for Sri Lankan document formats</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($documentTypes as $type)
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-lg font-semibold mb-2">{{ $type->name }}</h3>
                    <p class="text-gray-600 mb-4">{{ $type->description }}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-blue-600 font-semibold">{{ $type->formatted_price }}</span>
                        <span class="text-sm text-gray-500">{{ $type->processing_time_estimate_seconds }}s avg</span>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Simple Pricing</h2>
                <p class="text-xl text-gray-600">Choose the plan that fits your needs</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                @foreach($plans as $plan)
                <div class="bg-white p-8 rounded-lg shadow-lg {{ $plan->isPro() ? 'border-2 border-blue-500 relative' : '' }}">
                    @if($plan->isPro())
                        <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm">Most Popular</span>
                        </div>
                    @endif
                    
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">{{ $plan->name }}</h3>
                        <div class="mb-6">
                            <span class="text-4xl font-bold">{{ $plan->formatted_price }}</span>
                            @if(!$plan->isFree())
                                <span class="text-gray-600">/month</span>
                            @endif
                        </div>
                        <p class="text-gray-600 mb-6">{{ $plan->description }}</p>
                        
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                {{ number_format($plan->monthly_credits) }} credits/month
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                Up to {{ $plan->max_team_members }} team member{{ $plan->max_team_members > 1 ? 's' : '' }}
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                {{ $plan->api_rate_limit_per_minute }} API requests/minute
                            </li>
                            @if($plan->features)
                                @foreach($plan->features as $feature)
                                <li class="flex items-center">
                                    <i class="fas fa-check text-green-500 mr-3"></i>
                                    {{ $feature }}
                                </li>
                                @endforeach
                            @endif
                        </ul>
                        
                        @guest
                            <a href="{{ route('register') }}" class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition block text-center">
                                Get Started
                            </a>
                        @else
                            <a href="{{ route('dashboard') }}" class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition block text-center">
                                Go to Dashboard
                            </a>
                        @endguest
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-blue-600 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <h2 class="text-4xl font-bold mb-4">Ready to Get Started?</h2>
            <p class="text-xl mb-8">Join hundreds of Sri Lankan businesses using SmartOCR.lk</p>
            @guest
                <a href="{{ route('register') }}" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition">
                    Start Your Free Trial
                </a>
            @endguest
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">SmartOCR.lk</h3>
                    <p class="text-gray-400">Intelligent document processing for Sri Lankan businesses.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#features" class="hover:text-white">Features</a></li>
                        <li><a href="#pricing" class="hover:text-white">Pricing</a></li>
                        <li><a href="{{ route('demo2.index') }}" class="hover:text-white">Demo</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="{{ route('landing.contact') }}" class="hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white">Documentation</a></li>
                        <li><a href="#" class="hover:text-white">API Reference</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-white">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; {{ date('Y') }} SmartOCR.lk. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
