<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DemoController;
use App\Http\Controllers\Demo2Controller;
use App\Http\Controllers\DocumentUploadController;
use App\Http\Controllers\LandingController;
use Illuminate\Support\Facades\Route;

// Landing pages
Route::get('/', [LandingController::class, 'index'])->name('landing.index');
Route::get('/demo', [LandingController::class, 'demo'])->name('landing.demo');
Route::get('/contact', [LandingController::class, 'contact'])->name('landing.contact');
Route::post('/contact', [LandingController::class, 'submitContact'])->name('landing.contact.submit');

// Demo functionality
Route::post('/demo/process', [DemoController::class, 'process'])->name('demo.process');
Route::get('/demo/check-limit', [DemoController::class, 'checkLimit'])->name('demo.check-limit');

// Real OCR Demo functionality
Route::get('/demo2', [Demo2Controller::class, 'index'])->name('demo2.index');
Route::post('/demo2/process', [Demo2Controller::class, 'process'])->name('demo2.process');
Route::get('/demo2/check-limit', [Demo2Controller::class, 'checkLimit'])->name('demo2.check-limit');
Route::get('/admin/demo-stats', [Demo2Controller::class, 'stats'])->name('admin.demo-stats');

// API Documentation (Swagger) - L5Swagger handles this automatically

// Test route for debugging
Route::get('/test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'Application is working',
        'timestamp' => now(),
        'session_driver' => config('session.driver'),
        'app_url' => config('app.url'),
    ]);
});

// Authenticated routes
Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/documents', [DashboardController::class, 'documents'])->name('dashboard.documents');
    Route::get('/dashboard/upload', [DashboardController::class, 'upload'])->name('dashboard.upload');
    Route::post('/dashboard/upload', [DocumentUploadController::class, 'process'])->name('dashboard.upload.process');
    Route::get('/dashboard/documents/{uuid}', [DocumentUploadController::class, 'show'])->name('dashboard.documents.show');
    Route::get('/dashboard/usage', [DashboardController::class, 'usage'])->name('dashboard.usage');
    Route::get('/dashboard/api-tokens', [DashboardController::class, 'apiTokens'])->name('dashboard.api-tokens');
    Route::post('/dashboard/api-tokens', [DashboardController::class, 'storeApiToken'])->name('dashboard.api-tokens.store');
    Route::delete('/dashboard/api-tokens/{token}', [DashboardController::class, 'destroyApiToken'])->name('dashboard.api-tokens.destroy');

    // Admin routes (for users with admin email)
    Route::middleware('admin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'index'])->name('dashboard');
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::get('/documents', [AdminController::class, 'documents'])->name('documents');
        Route::get('/analytics', [AdminController::class, 'analytics'])->name('analytics');
        Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
    });
});
