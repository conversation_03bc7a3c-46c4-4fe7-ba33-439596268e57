<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ExternalOcrService
{
    private const API_BASE_URL = 'https://doc.ornsys.com';
    private const TIMEOUT = 60; // 60 seconds timeout

    public function processDocument(string $documentType, UploadedFile $mainFile, ?UploadedFile $additionalFile = null): array
    {
        $startTime = microtime(true);
        
        try {
            switch ($documentType) {
                case 'nic':
                    return $this->processNic($mainFile, $additionalFile, $startTime);
                case 'driving_license':
                    return $this->processDrivingLicense($mainFile, $startTime);
                case 'vehicle_registration':
                    return $this->processVehicleRegistration($mainFile, $startTime);
                default:
                    throw new \Exception("Unsupported document type: {$documentType}");
            }
        } catch (\Exception $e) {
            Log::error('External OCR processing failed', [
                'document_type' => $documentType,
                'error' => $e->getMessage(),
                'processing_time_ms' => round((microtime(true) - $startTime) * 1000),
            ]);
            
            throw $e;
        }
    }

    private function processNic(UploadedFile $frontFile, ?UploadedFile $backFile, float $startTime): array
    {
        $apiUrl = self::API_BASE_URL . '/apinic.php';
        
        // Prepare the data
        $postData = [
            'name' => $frontFile->getClientOriginalName(),
            'image' => base64_encode(file_get_contents($frontFile->getPathname())),
        ];

        // Add back image if provided
        if ($backFile) {
            $postData['name2'] = $backFile->getClientOriginalName();
            $postData['image2'] = base64_encode(file_get_contents($backFile->getPathname()));
        }

        $response = $this->makeApiCall($apiUrl, $postData);
        $processingTime = round((microtime(true) - $startTime) * 1000);

        return [
            'status' => 'completed',
            'document_type' => 'nic',
            'processing_time_ms' => $processingTime,
            'confidence_score' => $this->extractConfidenceScore($response),
            'result' => $this->parseNicResponse($response),
            'raw_response' => $response,
        ];
    }

    private function processDrivingLicense(UploadedFile $file, float $startTime): array
    {
        $apiUrl = self::API_BASE_URL . '/apidriv.php';
        
        $postData = [
            'name' => $file->getClientOriginalName(),
            'image' => base64_encode(file_get_contents($file->getPathname())),
        ];

        $response = $this->makeApiCall($apiUrl, $postData);
        $processingTime = round((microtime(true) - $startTime) * 1000);

        return [
            'status' => 'completed',
            'document_type' => 'driving_license',
            'processing_time_ms' => $processingTime,
            'confidence_score' => $this->extractConfidenceScore($response),
            'result' => $this->parseDrivingLicenseResponse($response),
            'raw_response' => $response,
        ];
    }

    private function processVehicleRegistration(UploadedFile $file, float $startTime): array
    {
        $apiUrl = self::API_BASE_URL . '/apivehi.php';
        
        $postData = [
            'name' => $file->getClientOriginalName(),
            'image' => base64_encode(file_get_contents($file->getPathname())),
        ];

        $response = $this->makeApiCall($apiUrl, $postData);
        $processingTime = round((microtime(true) - $startTime) * 1000);

        return [
            'status' => 'completed',
            'document_type' => 'vehicle_registration',
            'processing_time_ms' => $processingTime,
            'confidence_score' => $this->extractConfidenceScore($response),
            'result' => $this->parseVehicleRegistrationResponse($response),
            'raw_response' => $response,
        ];
    }

    private function makeApiCall(string $url, array $postData): string
    {
        $ch = curl_init($url);
        
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => self::TIMEOUT,
            CURLOPT_HTTPHEADER => ['Content-Type: application/x-www-form-urlencoded'],
            CURLOPT_POSTFIELDS => http_build_query($postData),
            CURLOPT_SSL_VERIFYPEER => false, // For development only
            CURLOPT_FOLLOWLOCATION => true,
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);

        if ($error) {
            throw new \Exception("cURL error: {$error}");
        }

        if ($httpCode !== 200) {
            throw new \Exception("HTTP error: {$httpCode}");
        }

        if ($response === false) {
            throw new \Exception("Empty response from OCR API");
        }

        return $response;
    }

    private function parseNicResponse(string $response): array
    {
        // Try to parse JSON response first
        $jsonData = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
            return $this->extractNicDataFromJson($jsonData);
        }
        
        // If not JSON, try to parse as text
        return $this->extractNicDataFromText($response);
    }

    private function parseDrivingLicenseResponse(string $response): array
    {
        $jsonData = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
            return $this->extractDrivingLicenseDataFromJson($jsonData);
        }
        
        return $this->extractDrivingLicenseDataFromText($response);
    }

    private function parseVehicleRegistrationResponse(string $response): array
    {
        $jsonData = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
            return $this->extractVehicleDataFromJson($jsonData);
        }
        
        return $this->extractVehicleDataFromText($response);
    }

    private function extractNicDataFromJson(array $data): array
    {
        return [
            'nic_number' => $data['nic_number'] ?? $data['id_number'] ?? 'Not found',
            'full_name' => $data['full_name'] ?? $data['name'] ?? 'Not found',
            'date_of_birth' => $data['date_of_birth'] ?? $data['dob'] ?? 'Not found',
            'address' => $data['address'] ?? 'Not found',
            'gender' => $data['gender'] ?? $data['sex'] ?? 'Not found',
        ];
    }

    private function extractNicDataFromText(string $text): array
    {
        // Basic text parsing - this would need to be improved based on actual API response format
        return [
            'nic_number' => $this->extractPattern($text, '/(\d{9}[VX]|\d{12})/'),
            'full_name' => $this->extractPattern($text, '/Name[:\s]+([A-Z\s]+)/i'),
            'date_of_birth' => $this->extractPattern($text, '/(\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4})/'),
            'address' => $this->extractPattern($text, '/Address[:\s]+([^,\n]+)/i'),
            'gender' => $this->extractPattern($text, '/(Male|Female|M|F)/i'),
            'raw_text' => $text,
        ];
    }

    private function extractDrivingLicenseDataFromJson(array $data): array
    {
        return [
            'license_number' => $data['license_number'] ?? $data['dl_number'] ?? 'Not found',
            'full_name' => $data['full_name'] ?? $data['name'] ?? 'Not found',
            'expiry_date' => $data['expiry_date'] ?? $data['valid_until'] ?? 'Not found',
            'vehicle_classes' => $data['vehicle_classes'] ?? $data['classes'] ?? [],
            'issued_date' => $data['issued_date'] ?? $data['issue_date'] ?? 'Not found',
        ];
    }

    private function extractDrivingLicenseDataFromText(string $text): array
    {
        return [
            'license_number' => $this->extractPattern($text, '/([A-Z]\d{7,8})/'),
            'full_name' => $this->extractPattern($text, '/Name[:\s]+([A-Z\s]+)/i'),
            'expiry_date' => $this->extractPattern($text, '/(\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4})/'),
            'vehicle_classes' => $this->extractPattern($text, '/Class[:\s]+([A-Z0-9,\s]+)/i'),
            'raw_text' => $text,
        ];
    }

    private function extractVehicleDataFromJson(array $data): array
    {
        return [
            'registration_number' => $data['registration_number'] ?? $data['reg_no'] ?? 'Not found',
            'vehicle_make' => $data['vehicle_make'] ?? $data['make'] ?? 'Not found',
            'vehicle_model' => $data['vehicle_model'] ?? $data['model'] ?? 'Not found',
            'year' => $data['year'] ?? $data['manufacture_year'] ?? 'Not found',
            'engine_number' => $data['engine_number'] ?? $data['engine_no'] ?? 'Not found',
            'chassis_number' => $data['chassis_number'] ?? $data['chassis_no'] ?? 'Not found',
        ];
    }

    private function extractVehicleDataFromText(string $text): array
    {
        return [
            'registration_number' => $this->extractPattern($text, '/([A-Z]{2,3}-[A-Z]{2}-\d{4})/'),
            'vehicle_make' => $this->extractPattern($text, '/Make[:\s]+([A-Z\s]+)/i'),
            'vehicle_model' => $this->extractPattern($text, '/Model[:\s]+([A-Z0-9\s]+)/i'),
            'year' => $this->extractPattern($text, '/(\d{4})/'),
            'engine_number' => $this->extractPattern($text, '/Engine[:\s]+([A-Z0-9]+)/i'),
            'chassis_number' => $this->extractPattern($text, '/Chassis[:\s]+([A-Z0-9]+)/i'),
            'raw_text' => $text,
        ];
    }

    private function extractPattern(string $text, string $pattern): string
    {
        if (preg_match($pattern, $text, $matches)) {
            return trim($matches[1] ?? $matches[0]);
        }
        
        return 'Not found';
    }

    private function extractConfidenceScore(string $response): float
    {
        // Try to extract confidence from JSON response
        $jsonData = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
            if (isset($jsonData['confidence'])) {
                return (float) $jsonData['confidence'];
            }
            if (isset($jsonData['accuracy'])) {
                return (float) $jsonData['accuracy'];
            }
        }
        
        // Default confidence score
        return 0.85;
    }
}
