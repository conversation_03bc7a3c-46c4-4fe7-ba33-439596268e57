<?php

namespace App\Services;

use App\Models\User;
use App\Models\Plan;
use App\Models\Payment;
use App\Models\ActivityLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AdminUserService
{
    /**
     * Get comprehensive usage statistics for a user
     */
    public function getUserUsageStats(User $user): array
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();
        
        return [
            'total_documents' => $user->documents()->count(),
            'documents_this_month' => $user->documents()->where('created_at', '>=', $currentMonth)->count(),
            'documents_last_month' => $user->documents()
                ->whereBetween('created_at', [$lastMonth, $currentMonth])
                ->count(),
            'successful_documents' => $user->documents()->where('status', 'completed')->count(),
            'failed_documents' => $user->documents()->where('status', 'failed')->count(),
            'total_credits_used' => $this->getTotalCreditsUsed($user),
            'api_calls_this_month' => $this->getApiCallsThisMonth($user),
            'last_activity' => $user->documents()->latest()->first()?->created_at,
            'document_types_usage' => $this->getDocumentTypesUsage($user),
            'monthly_usage_trend' => $this->getMonthlyUsageTrend($user),
        ];
    }

    /**
     * Get payment history for a user
     */
    public function getPaymentHistory(User $user): array
    {
        // For now, return mock data since we don't have payments table yet
        // TODO: Implement actual payment history when payments table is created
        return [
            'total_paid' => 0,
            'last_payment' => null,
            'payment_count' => 0,
            'payments' => [],
        ];
    }

    /**
     * Log credit changes
     */
    public function logCreditChange(User $user, int $oldCredits, int $newCredits, ?string $reason = null): void
    {
        $change = $newCredits - $oldCredits;
        $action = $change > 0 ? 'added' : 'subtracted';
        
        Log::info('Admin credit change', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'old_credits' => $oldCredits,
            'new_credits' => $newCredits,
            'change' => $change,
            'action' => $action,
            'reason' => $reason,
            'admin_user' => auth()->user()->email,
        ]);

        // TODO: Store in activity_logs table when created
    }

    /**
     * Log plan changes
     */
    public function logPlanChange(User $user, Plan $newPlan, ?string $reason = null): void
    {
        $oldPlan = $user->currentPlan?->plan;
        
        Log::info('Admin plan change', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'old_plan' => $oldPlan?->name,
            'new_plan' => $newPlan->name,
            'reason' => $reason,
            'admin_user' => auth()->user()->email,
        ]);

        // TODO: Store in activity_logs table when created
    }

    /**
     * Record offline payment
     */
    public function recordOfflinePayment(User $user, array $paymentData): object
    {
        // TODO: Create payments table and model
        // For now, just log the payment
        Log::info('Offline payment recorded', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'amount' => $paymentData['amount'],
            'currency' => $paymentData['currency'],
            'plan_slug' => $paymentData['plan_slug'],
            'duration_months' => $paymentData['duration_months'],
            'payment_method' => $paymentData['payment_method'],
            'reference' => $paymentData['reference'],
            'notes' => $paymentData['notes'],
            'admin_user' => auth()->user()->email,
        ]);

        // Return mock payment object
        return (object) [
            'id' => uniqid(),
            'amount' => $paymentData['amount'],
            'currency' => $paymentData['currency'],
            'created_at' => now(),
        ];
    }

    /**
     * Suspend user account
     */
    public function suspendUser(User $user, string $reason): void
    {
        DB::beginTransaction();
        
        try {
            // Deactivate current plan
            if ($user->currentPlan) {
                $user->currentPlan->update(['is_active' => false]);
            }

            // Revoke all API tokens
            $user->tokens()->delete();

            // Log the suspension
            Log::warning('User suspended by admin', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'reason' => $reason,
                'admin_user' => auth()->user()->email,
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Activate user account
     */
    public function activateUser(User $user, ?string $reason = null): void
    {
        DB::beginTransaction();
        
        try {
            // Reactivate current plan or assign free plan
            if ($user->currentPlan) {
                $user->currentPlan->update(['is_active' => true]);
            } else {
                app(PlanService::class)->assignFreePlanToUser($user);
            }

            // Log the activation
            Log::info('User activated by admin', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'reason' => $reason,
                'admin_user' => auth()->user()->email,
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get user usage logs
     */
    public function getUserUsageLogs(User $user): array
    {
        // Get recent documents with processing details
        $recentDocuments = $user->documents()
            ->with(['documentType', 'result'])
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get()
            ->map(function ($doc) {
                return [
                    'id' => $doc->id,
                    'type' => $doc->documentType->name,
                    'status' => $doc->status,
                    'credits_used' => $doc->credits_used,
                    'created_at' => $doc->created_at,
                    'processing_time' => $doc->processing_completed_at 
                        ? $doc->processing_completed_at->diffInSeconds($doc->created_at) 
                        : null,
                ];
            });

        // Get API token usage
        $apiUsage = $user->tokens()
            ->select('name', 'last_used_at', 'created_at')
            ->orderBy('last_used_at', 'desc')
            ->get()
            ->map(function ($token) {
                return [
                    'name' => $token->name,
                    'created_at' => $token->created_at,
                    'last_used_at' => $token->last_used_at,
                    'usage_frequency' => $token->last_used_at 
                        ? $token->last_used_at->diffInDays($token->created_at)
                        : 'Never used',
                ];
            });

        return [
            'recent_documents' => $recentDocuments,
            'api_usage' => $apiUsage,
            'summary' => [
                'total_api_calls' => $recentDocuments->count(),
                'success_rate' => $recentDocuments->count() > 0 
                    ? ($recentDocuments->where('status', 'completed')->count() / $recentDocuments->count()) * 100 
                    : 0,
                'avg_processing_time' => $recentDocuments->where('processing_time', '!=', null)->avg('processing_time'),
            ],
        ];
    }

    /**
     * Export users data
     */
    public function exportUsers(string $format, array $filters = []): \Illuminate\Http\Response
    {
        $query = User::with(['currentPlan.plan', 'teams', 'documents'])
                    ->where('email', '!=', '<EMAIL>');

        // Apply filters
        if (!empty($filters['plan'])) {
            $query->whereHas('currentPlan.plan', function($q) use ($filters) {
                $q->where('slug', $filters['plan']);
            });
        }

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $users = $query->get();

        $data = $users->map(function ($user) {
            return [
                'ID' => $user->id,
                'Name' => $user->name,
                'Email' => $user->email,
                'Plan' => $user->currentPlan?->plan->name ?? 'No Plan',
                'Credits Remaining' => $user->remaining_credits,
                'Total Documents' => $user->documents->count(),
                'Teams' => $user->teams->count(),
                'API Tokens' => $user->tokens->count(),
                'Registered' => $user->created_at->format('Y-m-d H:i:s'),
                'Last Activity' => $user->documents->max('created_at') ?? 'Never',
            ];
        });

        return $this->generateExport($data, $format, 'users_export_' . now()->format('Y-m-d'));
    }

    /**
     * Helper methods
     */
    private function getTotalCreditsUsed(User $user): int
    {
        return $user->documents()->sum('credits_used') ?? 0;
    }

    private function getApiCallsThisMonth(User $user): int
    {
        return $user->documents()
            ->where('created_at', '>=', now()->startOfMonth())
            ->count();
    }

    private function getDocumentTypesUsage(User $user): array
    {
        return $user->documents()
            ->join('document_types', 'documents.document_type_id', '=', 'document_types.id')
            ->select('document_types.name', DB::raw('count(*) as count'))
            ->groupBy('document_types.name')
            ->orderBy('count', 'desc')
            ->get()
            ->toArray();
    }

    private function getMonthlyUsageTrend(User $user): array
    {
        $months = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $count = $user->documents()
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->count();
            
            $months[] = [
                'month' => $month->format('M Y'),
                'count' => $count,
            ];
        }
        
        return $months;
    }

    private function generateExport($data, string $format, string $filename): \Illuminate\Http\Response
    {
        if ($format === 'csv') {
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}.csv\"",
            ];

            $callback = function() use ($data) {
                $file = fopen('php://output', 'w');
                
                // Add headers
                if ($data->isNotEmpty()) {
                    fputcsv($file, array_keys($data->first()));
                }
                
                // Add data
                foreach ($data as $row) {
                    fputcsv($file, $row);
                }
                
                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }

        // TODO: Implement Excel export
        throw new \Exception('Excel export not implemented yet');
    }
}
