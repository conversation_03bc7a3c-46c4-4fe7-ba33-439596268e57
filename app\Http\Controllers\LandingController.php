<?php

namespace App\Http\Controllers;

use App\Models\DocumentType;
use App\Models\Plan;
use Illuminate\Http\Request;

class LandingController extends Controller
{
    public function index()
    {
        $documentTypes = DocumentType::active()->get();
        $plans = Plan::active()->ordered()->get();

        return view('landing.index', compact('documentTypes', 'plans'));
    }

    public function demo()
    {
        if (!config('app.demo_enabled', true)) {
            abort(404);
        }

        $documentTypes = DocumentType::active()->get();

        return view('landing.demo', compact('documentTypes'));
    }

    public function contact()
    {
        return view('landing.contact');
    }

    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'company' => 'nullable|string|max:255',
            'message' => 'required|string|max:1000',
        ]);

        // Here you would typically send an email or store in database
        // For now, we'll just return a success message

        return back()->with('success', 'Thank you for your message. We will get back to you soon!');
    }
}
