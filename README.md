# SmartOCR.lk - Intelligent Document Processing SaaS

A comprehensive OCR (Optical Character Recognition) SaaS platform specifically designed for Sri Lankan documents, built with Laravel 11 and modern web technologies.

## 🚀 Features

### Core Functionality
- **Multi-Document Support**: Process NICs, driving licenses, vehicle registrations, land deeds, and invoices
- **High Accuracy**: Optimized for Sri Lankan document formats with 95%+ accuracy
- **Real-time Processing**: Fast document processing with live status updates
- **Secure Processing**: Documents auto-deleted after 24 hours for privacy compliance

### Business Features
- **Credit System**: Flexible usage-based pricing model
- **Team Collaboration**: Multi-user support with role-based access
- **API Integration**: RESTful API for seamless system integration
- **Analytics Dashboard**: Comprehensive usage and performance metrics
- **Admin Panel**: Complete system management interface

### Technical Features
- **Modern UI**: Responsive design with Tailwind CSS
- **Queue System**: Background processing for scalability
- **Rate Limiting**: Plan-based API rate limiting
- **Webhook Support**: Real-time notifications
- **File Management**: Secure upload and storage handling

## 🛠 Technology Stack

- **Backend**: Laravel 11 with PHP 8.2+
- **Frontend**: Livewire + Tailwind CSS + Alpine.js
- **Database**: MySQL 8.0+
- **Authentication**: Laravel Jetstream with Sanctum
- **File Storage**: Local/Cloud storage support
- **Queue System**: Redis-based job processing
- **Caching**: Redis for performance optimization

## 📋 Prerequisites

- PHP 8.2 or higher
- Composer 2.x
- Node.js 18+ and npm
- MySQL 8.0+
- Redis (recommended for queues and caching)
- Web server (Nginx/Apache)

## 🔧 Installation

### Local Development Setup (Windows XAMPP)

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/smartocr.git
   cd smartocr
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment Configuration**
   ```bash
   copy .env.example .env
   php artisan key:generate
   ```

5. **Configure your `.env` file for XAMPP**
   ```env
   APP_NAME="SmartOCR.lk"
   APP_ENV=local
   APP_DEBUG=true
   APP_URL=http://YOURDOMAIN

   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=smartocr
   DB_USERNAME=root
   DB_PASSWORD=

   QUEUE_CONNECTION=redis
   CACHE_STORE=redis

   # OCR Service Configuration
   OCR_SERVICE_URL=http://localhost:8001/api/process
   OCR_SERVICE_API_KEY=dev-key
   OCR_SERVICE_TIMEOUT=30

   # File Storage
   DOCUMENT_STORAGE_PATH=documents
   DOCUMENT_RETENTION_HOURS=24
   MAX_FILE_SIZE_MB=10

   # Rate Limiting (requests per minute)
   RATE_LIMIT_FREE=10
   RATE_LIMIT_PRO=60
   RATE_LIMIT_ENTERPRISE=200

   # Payment Configuration
   PAYMENT_CURRENCY=LKR
   PAYMENT_VERIFICATION_TIMEOUT_HOURS=72

   # Demo Configuration
   DEMO_DAILY_LIMIT=1
   DEMO_ENABLED=true
   ```

6. **Database Setup**
   ```bash
   # Create database using XAMPP MySQL
   H:\xampp82\mysql\bin\mysql.exe -u root -e "CREATE DATABASE IF NOT EXISTS smartocr CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

   # Run migrations and seeders
   php artisan migrate
   php artisan db:seed
   ```

7. **Build Assets**
   ```bash
   npm run build
   ```

8. **Access the Application**
   - Open browser and go to: `http://YOURDOMAIN/`
   - Admin login: `<EMAIL>` / `password`
   - Test user: `<EMAIL>` / `password`

## 📚 API Documentation

### Authentication

All API requests require authentication using Laravel Sanctum tokens.

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "token": "1|abc123...",
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>"
        },
        "expires_at": "2024-02-15T10:30:00.000000Z"
    }
}
```

#### Upload Document
```http
POST /api/v1/documents/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
    "file": (binary),
    "document_type": "nic",
    "additional_file": (binary, optional),
    "webhook_url": "https://your-app.com/webhook" (optional)
}
```

### Document Types

| Type | Slug | Description | Credits |
|------|------|-------------|---------|
| National Identity Card | `nic` | Sri Lankan NIC (front/back) | 1 |
| Driving License | `driving_license` | Sri Lankan Driving License | 1 |
| Vehicle Registration | `vehicle_registration` | Vehicle Registration Certificate | 2 |
| Land Deed | `land_deed` | Property Land Deed Document | 3 |
| Invoice | `invoice` | Commercial Invoice Document | 1 |

## 🔒 Security Features

- **Data Protection**: Documents auto-deleted after 24 hours
- **API Security**: Rate limiting and authentication required
- **Input Validation**: Comprehensive validation and sanitization
- **CSRF Protection**: Cross-site request forgery protection
- **SQL Injection Prevention**: Eloquent ORM protection
- **XSS Protection**: Output escaping and content security
- **Secure File Upload**: File type and size validation
- **SSL/TLS**: HTTPS encryption for all communications

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Email**: <EMAIL>
- **Documentation**: [docs.smartocr.lk](https://docs.smartocr.lk)
- **Issues**: Create an issue in this repository
- **Business Hours**: Monday-Friday 9:00 AM - 6:00 PM (Sri Lanka Time)

---

**Built with ❤️ for Sri Lankan businesses**
