<?php

namespace Database\Seeders;

use App\Models\Plan;
use App\Models\User;
use App\Models\UserPlan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $freePlan = Plan::where('slug', 'free')->first();

        if (!$freePlan) {
            $this->command->error('Free plan not found. Please run PlansSeeder first.');
            return;
        }

        // Assign free plan to all users who don't have a plan
        $usersWithoutPlan = User::whereDoesntHave('userPlans')->get();

        foreach ($usersWithoutPlan as $user) {
            UserPlan::create([
                'user_id' => $user->id,
                'plan_id' => $freePlan->id,
                'starts_at' => now(),
                'expires_at' => null, // Free plan doesn't expire
                'is_active' => true,
            ]);

            // Update user's remaining credits
            $user->update([
                'remaining_credits' => $freePlan->monthly_credits,
            ]);

            $this->command->info("Assigned free plan to user: {$user->email}");
        }
    }
}
