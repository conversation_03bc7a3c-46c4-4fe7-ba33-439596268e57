<?php

namespace Database\Seeders;

use App\Models\Plan;
use App\Models\User;
use App\Models\UserPlan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $freePlan = Plan::where('slug', 'free')->first();

        if (!$freePlan) {
            $this->command->error('Free plan not found. Please run PlansSeeder first.');
            return;
        }

        // Assign free plan to all users who don't have a plan
        $usersWithoutPlan = User::whereDoesntHave('userPlans')->get();

        foreach ($usersWithoutPlan as $user) {
            UserPlan::create([
                'user_id' => $user->id,
                'plan_id' => $freePlan->id,
                'credits_remaining' => $freePlan->monthly_credits,
                'credits_used_this_month' => 0,
                'billing_cycle_start' => now()->startOfMonth(),
                'billing_cycle_end' => now()->endOfMonth(),
                'is_active' => true,
            ]);

            $this->command->info("Assigned free plan to user: {$user->email}");
        }
    }
}
