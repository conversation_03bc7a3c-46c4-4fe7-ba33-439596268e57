# SmartOCR.lk Testing Guide

## Overview

This guide provides comprehensive testing procedures for the SmartOCR.lk platform, covering both manual testing and automated testing scenarios.

## Test Environment Setup

### Prerequisites
- Laravel application running locally or on test server
- Test database with seeded data
- Sample document files for testing
- Admin and user test accounts

### Test Accounts

#### Admin Account
- **Email:** `<EMAIL>`
- **Password:** `password`
- **Permissions:** Full system access, admin panel, demo statistics

#### Test User Account
- **Email:** `<EMAIL>`
- **Password:** `password`
- **Plan:** Free (50 credits)

## Manual Testing Procedures

### 1. Landing Page Testing

#### Test Cases
1. **Homepage Load**
   - Navigate to `/`
   - Verify all sections load correctly
   - Check responsive design on mobile/tablet
   - Test all navigation links

2. **Demo Page Access**
   - Navigate to `/demo` (simulated demo)
   - Navigate to `/demo2` (real OCR demo)
   - Verify both pages load without errors

3. **Contact Form**
   - Navigate to `/contact`
   - Fill out contact form
   - Submit and verify success message

### 2. Authentication Testing

#### Registration Flow
1. Navigate to `/register`
2. Fill registration form with valid data
3. Submit and verify account creation
4. Check email verification (if enabled)
5. Verify automatic free plan assignment

#### Login Flow
1. Navigate to `/login`
2. Test with valid credentials
3. Test with invalid credentials
4. Verify redirect to dashboard after login

#### Password Reset
1. Navigate to `/forgot-password`
2. Enter email and submit
3. Check password reset email
4. Follow reset link and set new password

### 3. Demo System Testing

#### Simulated Demo (`/demo`)
1. **First Request**
   - Upload a test document
   - Select document type
   - Submit and verify simulated results
   - Check database entry in `demo_requests`

2. **Rate Limiting**
   - Try to upload another document from same IP
   - Verify "Demo limit exceeded" error
   - Check admin stats show the requests

#### Real OCR Demo (`/demo2`)
1. **NIC Processing**
   - Upload NIC front image
   - Upload NIC back image (optional)
   - Submit and verify real OCR processing
   - Check response format and data

2. **Driving License Processing**
   - Upload driving license image
   - Submit and verify processing
   - Verify single image handling

3. **Vehicle Registration Processing**
   - Upload vehicle registration image
   - Submit and verify processing
   - Check extracted vehicle data

4. **API Failure Handling**
   - Test with invalid image
   - Verify fallback to simulated results
   - Check error logging

### 4. User Dashboard Testing

#### Dashboard Overview
1. Login as test user
2. Navigate to `/dashboard`
3. Verify credit balance display
4. Check recent documents section
5. Test quick action buttons

#### Document Upload
1. Navigate to `/dashboard/upload`
2. Test each document type:
   - National Identity Card
   - Driving License
   - Vehicle Registration
   - Land Deed
   - Invoice
3. Verify file validation
4. Test additional file upload for NIC
5. Check credit deduction

#### Document Management
1. Navigate to `/dashboard/documents`
2. Test document filters
3. Verify document status display
4. Test document detail view
5. Check result download functionality

#### Usage Analytics
1. Navigate to `/dashboard/usage`
2. Verify usage statistics
3. Check plan information
4. Test usage charts

### 5. Admin Panel Testing

#### Admin Dashboard
1. Login as admin
2. Navigate to `/admin/dashboard`
3. Verify system statistics
4. Check recent activity
5. Test quick action links

#### User Management
1. Navigate to `/admin/users`
2. View user list
3. Test user search/filter
4. Check user details
5. Verify plan assignments

#### Demo Statistics
1. Navigate to `/admin/demo-stats`
2. Verify demo request statistics
3. Check document type breakdown
4. Test export functionality
5. Review recent requests table

### 6. API Testing

#### Authentication
```bash
# Test login
curl -X POST http://localhost/Multiblity/2025/smartocr/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

#### Document Upload
```bash
# Test document upload
curl -X POST http://localhost/Multiblity/2025/smartocr/api/v1/documents/upload \
  -H "Authorization: Bearer {token}" \
  -F "file=@test-nic.jpg" \
  -F "document_type=nic"
```

#### Get Document Result
```bash
# Test document retrieval
curl -X GET http://localhost/Multiblity/2025/smartocr/api/v1/documents/{uuid} \
  -H "Authorization: Bearer {token}"
```

## Automated Testing

### Unit Tests

#### Model Tests
```php
// Test User model
public function test_user_can_have_current_plan()
{
    $user = User::factory()->create();
    $plan = Plan::factory()->create();
    
    UserPlan::create([
        'user_id' => $user->id,
        'plan_id' => $plan->id,
        'is_active' => true,
    ]);
    
    $this->assertInstanceOf(UserPlan::class, $user->currentPlan);
}

// Test Document model
public function test_document_belongs_to_user()
{
    $document = Document::factory()->create();
    $this->assertInstanceOf(User::class, $document->user);
}
```

#### Service Tests
```php
// Test OCR Service
public function test_ocr_service_processes_document()
{
    $file = UploadedFile::fake()->image('test.jpg');
    $result = $this->ocrService->processDocument('nic', $file);
    
    $this->assertArrayHasKey('status', $result);
    $this->assertEquals('completed', $result['status']);
}
```

### Feature Tests

#### Demo System Tests
```php
public function test_demo_rate_limiting()
{
    // First request should succeed
    $response = $this->post('/demo/process', [
        'file' => UploadedFile::fake()->image('test.jpg'),
        'document_type' => 'nic'
    ]);
    
    $response->assertStatus(200);
    
    // Second request should fail
    $response = $this->post('/demo/process', [
        'file' => UploadedFile::fake()->image('test2.jpg'),
        'document_type' => 'nic'
    ]);
    
    $response->assertStatus(429);
}
```

#### API Tests
```php
public function test_api_requires_authentication()
{
    $response = $this->postJson('/api/v1/documents/upload');
    $response->assertStatus(401);
}

public function test_authenticated_user_can_upload_document()
{
    $user = User::factory()->create();
    $token = $user->createToken('test')->plainTextToken;
    
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
    ])->post('/api/v1/documents/upload', [
        'file' => UploadedFile::fake()->image('test.jpg'),
        'document_type' => 'nic'
    ]);
    
    $response->assertStatus(200);
}
```

## Performance Testing

### Load Testing
```bash
# Test concurrent demo requests
ab -n 100 -c 10 -p test-data.json -T application/json \
  http://localhost/Multiblity/2025/smartocr/demo/process

# Test API endpoints
ab -n 1000 -c 50 -H "Authorization: Bearer {token}" \
  http://localhost/Multiblity/2025/smartocr/api/v1/documents
```

### Database Performance
```sql
-- Check demo requests query performance
EXPLAIN SELECT * FROM demo_requests 
WHERE ip_address = '127.0.0.1' 
AND DATE(created_at) = CURDATE();

-- Check document queries
EXPLAIN SELECT * FROM documents 
WHERE user_id = 1 
ORDER BY created_at DESC 
LIMIT 10;
```

## Security Testing

### Input Validation
1. Test file upload with invalid file types
2. Test oversized file uploads
3. Test malicious file uploads
4. Verify CSRF protection
5. Test SQL injection attempts

### Authentication Security
1. Test token expiration
2. Verify rate limiting enforcement
3. Test unauthorized access attempts
4. Check password security requirements

## Test Data

### Sample Documents
- `test-nic-front.jpg` - Clear NIC front image
- `test-nic-back.jpg` - Clear NIC back image
- `test-license.jpg` - Driving license image
- `test-vehicle.jpg` - Vehicle registration image
- `test-invalid.txt` - Invalid file type
- `test-large.jpg` - Oversized image (>10MB)

### Test Scripts
```bash
#!/bin/bash
# run-tests.sh

echo "Running Laravel tests..."
php artisan test

echo "Running demo rate limit test..."
curl -X POST http://localhost/Multiblity/2025/smartocr/demo2/process \
  -F "file=@test-nic-front.jpg" \
  -F "document_type=nic"

echo "Testing API authentication..."
curl -X GET http://localhost/Multiblity/2025/smartocr/api/v1/documents

echo "All tests completed!"
```

## Troubleshooting Common Issues

### Demo Not Working
1. Check database connection
2. Verify `demo_requests` table exists
3. Check file permissions
4. Review error logs

### OCR API Failures
1. Verify external API connectivity
2. Check API endpoint URLs
3. Validate image encoding
4. Review timeout settings

### Performance Issues
1. Check database indexes
2. Monitor memory usage
3. Review query performance
4. Check file storage space

## Test Reporting

### Test Results Format
```json
{
    "test_suite": "SmartOCR Demo System",
    "timestamp": "2024-01-15T10:00:00Z",
    "results": {
        "total_tests": 45,
        "passed": 43,
        "failed": 2,
        "skipped": 0
    },
    "failed_tests": [
        {
            "name": "test_external_ocr_api_timeout",
            "error": "Connection timeout after 60 seconds"
        }
    ]
}
```

### Coverage Report
- Unit Tests: 85% coverage
- Feature Tests: 90% coverage
- API Tests: 95% coverage
- Demo System: 100% coverage

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.2
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: php artisan test
```

This testing guide ensures comprehensive coverage of all system components and provides both manual and automated testing procedures for maintaining system quality and reliability.
