# SmartOCR.lk - Usage Guide

## 🚀 Getting Started

### Access URLs
- **Main Application:** `http://localhost/Multiblity/2025/smartocr/`
- **Landing Page:** `http://localhost/Multiblity/2025/smartocr/`
- **Demo Page:** `http://localhost/Multiblity/2025/smartocr/demo`
- **Admin Panel:** `http://localhost/Multiblity/2025/smartocr/admin/dashboard`

### Test Accounts

#### Admin Account
- **Email:** `<EMAIL>`
- **Password:** `password`
- **Access:** Full admin privileges, system management

#### Regular User Account
- **Email:** `<EMAIL>`
- **Password:** `password`
- **Access:** Standard user features, 50 free credits

## 👤 User Guide

### 1. Registration & Login

#### New User Registration
1. Visit the homepage
2. Click "Get Started" or "Register"
3. Fill in your details:
   - Name
   - Email
   - Password (min 8 characters)
   - Confirm password
4. Accept terms and conditions
5. Click "Register"
6. You'll automatically get a **Free Plan** with 50 credits

#### Login Process
1. Click "Login" from any page
2. Enter your email and password
3. Click "Log In"
4. You'll be redirected to your dashboard

### 2. Dashboard Overview

#### Main Dashboard Features
- **Credits Remaining:** Shows your current credit balance
- **Recent Documents:** Last 5 uploaded documents
- **Usage Statistics:** Total, completed, and pending documents
- **Quick Actions:** Upload, view documents, analytics, API tokens

#### Navigation Menu
- **Dashboard:** Main overview page
- **Upload:** Upload new documents for processing
- **Documents:** View and manage all your documents
- **Usage:** Analytics and usage statistics
- **Profile:** Account settings and team management

### 3. Document Upload Process

#### Step-by-Step Upload
1. Go to **Dashboard → Upload**
2. Select document type from dropdown:
   - National Identity Card (1 credit)
   - Driving License (1 credit)
   - Vehicle Registration (2 credits)
   - Land Deed (3 credits)
   - Invoice (1 credit)
3. Upload main file (drag & drop or click to browse)
4. Upload additional file if supported (e.g., back of NIC)
5. Optional: Add webhook URL for notifications
6. Click "Process Document"

#### Supported File Formats
- **Images:** JPG, JPEG, PNG
- **Documents:** PDF
- **Max Size:** 10MB per file

### 4. Document Management

#### Viewing Documents
1. Go to **Dashboard → Documents**
2. Use filters to find specific documents:
   - Status (pending, processing, completed, failed)
   - Document type
   - Date range
3. Click "View" to see detailed results

#### Document Status Types
- **Pending:** Waiting to be processed
- **Processing:** Currently being analyzed
- **Completed:** Successfully processed with results
- **Failed:** Processing failed (error message shown)
- **Manual Review:** Requires human verification

#### Downloading Results
- Click "Download" next to completed documents
- Results available in JSON format
- Copy raw JSON data from document view page

### 5. Usage Analytics

#### Analytics Dashboard
1. Go to **Dashboard → Usage**
2. View your usage statistics:
   - Credits used vs. remaining
   - Success rate percentage
   - Documents by type breakdown
   - Usage over time chart

#### Plan Information
- Current plan details
- Billing cycle information
- Feature comparison
- Upgrade options

### 6. Team Management (Jetstream)

#### Creating Teams
1. Click your name in top navigation
2. Select "Create New Team"
3. Enter team name and create
4. Invite team members via email

#### Team Features
- Shared document access
- Team-based credit usage
- Role-based permissions
- Collaborative processing

## 🔧 Admin Guide

### Admin Access
1. Login with admin account: `<EMAIL>` / `password`
2. Access admin panel: `http://localhost/Multiblity/2025/smartocr/admin/dashboard`

### Admin Dashboard Features

#### System Overview
- Total users count
- Total documents processed
- Documents processed today
- Active users (last 30 days)

#### User Management
1. Go to **Admin → Users**
2. View all registered users
3. Search users by name or email
4. View user details and activity
5. Monitor user plan usage

#### Document Management
1. Go to **Admin → Documents**
2. View all processed documents
3. Filter by status, type, or user
4. Monitor processing performance
5. Handle failed documents

#### Analytics & Reports
1. Go to **Admin → Analytics**
2. View system-wide statistics:
   - Daily usage trends
   - Success rates by document type
   - Plan usage distribution
   - Performance metrics

#### System Settings
1. Go to **Admin → Settings**
2. Manage document types
3. Configure pricing plans
4. System configuration options

### Admin Privileges
- View all user data
- Access system analytics
- Manage document types and plans
- Monitor system health
- Handle support requests

## 🌐 Demo Usage

### Public Demo Features
1. Visit: `http://localhost/Multiblity/2025/smartocr/demo`
2. No registration required
3. **Limitation:** 1 document per IP address per day
4. Select document type and upload file
5. View simulated OCR results
6. Encourages registration for full access

### Demo Limitations
- Limited to 1 upload per IP per day
- Simulated results (not real OCR)
- No result storage or download
- No webhook support

## 🔌 API Usage

### Authentication
1. Login via API or web interface
2. Go to **Dashboard → API Tokens** (or use API login)
3. Create new token with desired permissions
4. Use token in Authorization header: `Bearer {token}`

### Demo Rate Limiting Status
Both demo systems (`/demo` and `/demo2`) enforce **1 request per IP address per day**:
- Tracked in database (`demo_requests` table)
- Resets at midnight
- Admin can monitor via `/admin/demo-stats`
- Real OCR integration on `/demo2` with fallback to simulation

### API Endpoints

#### Authentication
```bash
# Login
POST /api/v1/auth/login
Content-Type: application/json
{
    "email": "<EMAIL>",
    "password": "password"
}

# Get user info
GET /api/v1/auth/user
Authorization: Bearer {token}
```

#### Document Processing
```bash
# Upload document
POST /api/v1/documents/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: (binary file)
document_type: "nic"
additional_file: (optional binary file)
webhook_url: "https://your-app.com/webhook" (optional)

# Get document result
GET /api/v1/documents/{uuid}
Authorization: Bearer {token}

# List documents
GET /api/v1/documents?status=completed&page=1
Authorization: Bearer {token}
```

#### Usage Analytics
```bash
# Get usage statistics
GET /api/v1/usage
Authorization: Bearer {token}

# Get usage history
GET /api/v1/usage/history?from=2024-01-01&to=2024-01-31
Authorization: Bearer {token}
```

### Rate Limits
- **Free Plan:** 10 requests/minute
- **Pro Plan:** 60 requests/minute
- **Enterprise Plan:** 200 requests/minute

## 🛠 Troubleshooting

### Common Issues

#### "Insufficient Credits" Error
- Check your current plan and credit balance
- Upgrade plan if needed
- Contact admin for credit adjustment

#### Upload Fails
- Check file size (max 10MB)
- Verify file format (JPG, PNG, PDF only)
- Ensure stable internet connection

#### Processing Stuck
- Wait 2-3 minutes for processing
- Refresh page to check status
- Contact support if stuck over 5 minutes

#### Demo Not Working
- Check if you've exceeded daily limit (1 per IP)
- Try different document type
- Clear browser cache and try again

### Getting Help

#### Support Channels
- **Email:** <EMAIL>
- **Contact Form:** Available on website
- **Business Hours:** Monday-Friday 9:00 AM - 6:00 PM (Sri Lanka Time)

#### Documentation
- **API Docs:** Available in dashboard
- **Technical Guide:** README.md
- **Deployment Guide:** docs/deployment-guide.md

## 📊 Plan Comparison

| Feature | Free | Pro | Enterprise |
|---------|------|-----|------------|
| Monthly Credits | 50 | 1,000 | 5,000 |
| Team Members | 1 | 5 | 50 |
| API Rate Limit | 10/min | 60/min | 200/min |
| Support | Email | Priority | Dedicated |
| Price | Free | LKR 10,000/month | LKR 25,000/month |

## 🔐 Security Best Practices

### For Users
- Use strong passwords
- Don't share API tokens
- Log out from shared computers
- Monitor your usage regularly

### For Admins
- Regularly review user activity
- Monitor system logs
- Keep software updated
- Backup data regularly

## 📱 Mobile Usage

### Mobile Web Access
- Fully responsive design
- Touch-friendly interface
- Mobile-optimized upload
- Swipe gestures supported

### Mobile Limitations
- Large file uploads may be slower
- Camera capture not yet implemented
- Some admin features desktop-only

---

**Need more help?** Contact our support <NAME_EMAIL> or use the contact form on our website.
