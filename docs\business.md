Great — based on all your inputs, here is the **complete and formal document** for your project:

---

# 🇱🇰 SmartOCR.lk – Sri Lanka-Based Smart OCR SaaS Platform

## Executive Summary

**SmartOCR.lk** is a Sri Lankan SaaS platform offering intelligent OCR (Optical Character Recognition) services tailored for local industries such as insurance, banking, logistics, and real estate. Users can upload or send document images (NICs, driving licenses, vehicle registrations, land deeds, invoices, etc.) through a user panel or API. The system processes these using a custom OCR backend (built on Google Vision and OpenAI, abstracted from end-users), returning structured JSON data. It supports team collaboration, credit-based billing, offline payment handling, and scalable packages like Free, Pro, and Enterprise.

The platform is built using **Laravel Jetstream** (with Livewire, team support) and designed for high extensibility, privacy compliance, and local relevance.

---

## 1. Core Features

### 1.1 Document Processing

* Upload via dashboard or API
* Supported document types:

  * NIC
  * Driving License
  * Vehicle Registration
  * Land Deed
  * Invoices
  * Utility Bills (planned)
* Return structured JSON with extracted key-value pairs
* Support image formats (JPG, PNG) and PDF
* Each document is classified by type for correct processing

### 1.2 Credit System

* Documents consume credits based on type:

  | Document Type         | Credit Cost |
  | --------------------- | ----------- |
  | NIC, License, Vehicle | 1 credit    |
  | Land Deed, Legal Doc  | 3 credits   |
  | Invoices (simple)     | 2 credits   |

* Credit usage tracked per document

* Admin can define new document types and credit values

### 1.3 Plans & Packages

| Plan       | Monthly Fee (LKR) | Credits | Team Support | Custom Docs | API Access | Role Control |
| ---------- | ----------------- | ------- | ------------ | ----------- | ---------- | ------------ |
| Free       | 0                 | 50      | No           | No          | Yes        | No           |
| Pro        | 10,000            | 1,000   | Yes          | No          | Yes        | Yes          |
| Enterprise | From 25,000       | Custom  | Yes          | Yes         | Yes        | Yes          |

* Additional credits: Buy at custom rates defined per package
* Admin can assign pricing and packages manually
* Admin approves Free → Pro or Enterprise upgrade manually

### 1.4 User Roles & Teams

* Teams supported via Laravel Jetstream
* Team roles:

  * **Admin**: Full access to team, settings, API keys
  * **Uploader**: Upload documents only
  * **Reviewer**: Access manual review panel
  * **Account**: View usage, billing
* Team Admins can create/manage users and assign roles

---

## 2. Technical Architecture

### 2.1 Backend

* **Laravel 11** with Jetstream + Livewire
* Sanctum for API token auth
* Core OCR pipeline via internal APIs (abstracting Google Vision + OpenAI)
* Queued document processing for async OCR

### 2.2 Frontend

* Laravel Blade with Livewire for interactive dashboards
* Responsive UI for mobile + desktop

### 2.3 Database Schema (MySQL)

#### Tables:

* `users`, `teams`, `team_user` (Jetstream standard)
* `documents`

  * id, user\_id, team\_id, file\_path, type, status, credit\_used, created\_at
* `document_usages`

  * document\_id, result\_json, error, processed\_at
* `api_keys`

  * user\_id, team\_id, key, usage\_limit, etc.
* `credit_transactions`

  * user\_id/team\_id, amount, description, created\_at
* `plans`, `plan_prices`, `plan_document_types`
* `offline_payments`

  * user\_id, reference\_no, amount, status, note

### 2.4 File Storage

* Document uploads saved via Laravel Filesystem (local or S3)
* Auto-delete old files after 24 hours (keep metadata only)

---

## 3. Admin Panel

### Admin Can:

* Create/manage plans
* Assign plans and credits
* Enable/disable orgs or users
* Approve offline payments
* Define credit cost per document type
* View system-wide usage logs
* Manage support requests

---

## 4. User Dashboard

### Features:

* Upload document manually
* View document history
* Monitor credit usage by:

  * Document type
  * Date range
  * Team member
* Download JSON or CSV
* Create API keys
* Bulk Upload (ZIP or multiple files)
* Webhook URL configuration (Enterprise)
* Manual Review Panel
* Create & manage Data Mapping Templates

---

## 5. API

### Endpoints:

* `POST /api/upload` — upload doc, specify type
* `GET /api/document/{id}` — get result
* `GET /api/usage` — get usage stats
* `POST /api/webhook/register` — set webhook for results

### Authentication:

* Token via Sanctum
* Rate limit based on plan

---

## 6. Landing Page & Demo Page

### Home Page (Public):

* About service, supported documents
* Credit & package pricing table
* Testimonials
* Benefits for banks, insurance, logistics
* Registration form (assign Free plan)
* Contact us form

### Demo Page:

* Not publicly linked
* Accessible via direct link when requested
* 1 attempt/day per IP (with Redis tracking)
* Upload demo document and view OCR JSON

---

## 7. Payments

### Offline Payments:

* Manual payment instructions provided (Bank deposit, etc.)
* Users submit payment reference in dashboard
* Admin reviews and confirms credit or plan upgrade

### Future Extensions:

* Add PayHere, WebXpay, Stripe integrations modularly

---

## 8. Additional Features

* ✅ **Bulk Upload**: Upload ZIP or multiple documents
* ✅ **Webhook Support**: For auto-result pushing
* ✅ **Manual Review Panel**: Human validation for sensitive docs
* ✅ **Data Mapping Templates**: User-defined output formats
* ✅ **Audit Logs**: Document activity tracking (user, timestamp)
* ✅ **Notification System**: Email alerts for processing complete/errors

---

## 9. Compliance & Privacy

* Document files stored max 24 hrs
* JSON + usage history kept for analytics
* Compliant with Sri Lanka’s **Personal Data Protection Act**
* No use of data for AI training
* Admin can generate compliance reports
* No mention of Google/OpenAI in frontend or public documentation

---

## 10. Deployment & Scalability

* Laravel Queues with Horizon for background jobs
* Redis cache + rate limiter
* Cloud-ready architecture (can host on local VPS or AWS)
* Support for horizontal scaling
* Can separate OCR module as a microservice later

---

## 11. Development Notes

* Modular system: Easily add document types, payment methods, review features
* Extendable API: Document webhook, credit callbacks, analytics hooks
* Admin UI with Voyager or custom Livewire panel
* Initial development: 2 developers, \~3 months

---

## 12. Project Summary

| Module                    | Status            |
| ------------------------- | ----------------- |
| OCR Backend               | ✅ Ready           |
| Laravel Base + Jetstream  | ⚙️ Starting Point |
| Credit System             | ✅ Planned         |
| API System                | ✅ Designed        |
| Admin Features            | ✅ Detailed        |
| Frontend                  | ✅ Planned         |
| Payment (Offline)         | ✅ Included        |
| Payment (Online - Future) | 🔄 Extendable     |
| Compliance                | ✅ Compliant       |
| Security                  | ✅ Scoped          |
