<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('team_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('document_type_id')->constrained('document_types');
            $table->string('file_path', 500);
            $table->string('additional_file_path', 500)->nullable();
            $table->string('original_filename', 255);
            $table->bigInteger('file_size_bytes');
            $table->string('mime_type', 100);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'manual_review'])->default('pending');
            $table->integer('credits_used')->default(0);
            $table->timestamp('processing_started_at')->nullable();
            $table->timestamp('processing_completed_at')->nullable();
            $table->decimal('confidence_score', 3, 2)->nullable();
            $table->string('webhook_url', 500)->nullable();
            $table->integer('webhook_attempts')->default(0);
            $table->timestamp('webhook_last_attempt_at')->nullable();
            $table->boolean('webhook_success')->default(false);
            $table->text('error_message')->nullable();
            $table->text('manual_review_reason')->nullable();
            $table->foreignId('manual_review_assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index('user_id');
            $table->index('team_id');
            $table->index('status');
            $table->index('created_at');
            $table->index('expires_at');
            $table->index('uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
