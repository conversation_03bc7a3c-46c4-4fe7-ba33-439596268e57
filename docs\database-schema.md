# Database Schema Documentation

## Overview
Complete database schema for SmartOCR.lk platform built on Laravel 11 with Jetstream team support.

## Core Tables

### Users & Authentication (Laravel Jetstream)

```sql
-- Standard Jetstream tables (auto-generated)
-- users, teams, team_user, team_invitations, personal_access_tokens
```

### Document Types Configuration

```sql
CREATE TABLE document_types (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    credit_cost INT NOT NULL DEFAULT 1,
    max_file_size_mb INT NOT NULL DEFAULT 10,
    allowed_extensions JSON NOT NULL DEFAULT '["jpg","jpeg","png","pdf"]',
    supports_additional_file BOOLEAN DEFAULT FALSE,
    processing_time_estimate_seconds INT DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    validation_rules JSO<PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_slug (slug),
    INDEX idx_active (is_active)
);
```

### Documents

```sql
CREATE TABLE documents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    team_id BIGINT NULL,
    document_type_id BIGINT NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    additional_file_path VARCHAR(500) NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'manual_review') DEFAULT 'pending',
    credits_used INT NOT NULL DEFAULT 0,
    processing_started_at TIMESTAMP NULL,
    processing_completed_at TIMESTAMP NULL,
    confidence_score DECIMAL(3,2) NULL,
    webhook_url VARCHAR(500) NULL,
    webhook_attempts INT DEFAULT 0,
    webhook_last_attempt_at TIMESTAMP NULL,
    webhook_success BOOLEAN DEFAULT FALSE,
    error_message TEXT NULL,
    manual_review_reason TEXT NULL,
    manual_review_assigned_to BIGINT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE SET NULL,
    FOREIGN KEY (document_type_id) REFERENCES document_types(id),
    FOREIGN KEY (manual_review_assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_team_id (team_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at),
    INDEX idx_uuid (uuid)
);
```

### Document Results

```sql
CREATE TABLE document_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_id BIGINT NOT NULL UNIQUE,
    extracted_data JSON NOT NULL,
    raw_ocr_response JSON,
    processing_time_ms INT,
    validation_errors JSON,
    manual_corrections JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_document_id (document_id)
);
```

### Credit System

```sql
CREATE TABLE plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    monthly_credits INT NOT NULL,
    price_lkr DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    max_team_members INT DEFAULT 1,
    api_rate_limit_per_minute INT DEFAULT 60,
    features JSON,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_slug (slug),
    INDEX idx_active (is_active)
);

CREATE TABLE user_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    plan_id BIGINT NOT NULL,
    credits_remaining INT NOT NULL DEFAULT 0,
    credits_used_this_month INT DEFAULT 0,
    billing_cycle_start DATE NOT NULL,
    billing_cycle_end DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES plans(id),
    INDEX idx_user_id (user_id),
    INDEX idx_active (is_active)
);

CREATE TABLE credit_transactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    team_id BIGINT NULL,
    document_id BIGINT NULL,
    type ENUM('debit', 'credit', 'refund', 'bonus') NOT NULL,
    amount INT NOT NULL,
    balance_after INT NOT NULL,
    description VARCHAR(255),
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE SET NULL,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);
```

### Payment System

```sql
CREATE TABLE payment_methods (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    type ENUM('bank_transfer', 'online', 'mobile_payment') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    configuration JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE payments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    payment_method_id BIGINT NOT NULL,
    amount_lkr DECIMAL(10,2) NOT NULL,
    credits_purchased INT NOT NULL,
    reference_number VARCHAR(100),
    bank_slip_path VARCHAR(500),
    status ENUM('pending', 'verified', 'rejected', 'expired') DEFAULT 'pending',
    admin_notes TEXT,
    verified_by BIGINT NULL,
    verified_at TIMESTAMP NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id),
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_reference (reference_number)
);
```

### Analytics & Usage

```sql
CREATE TABLE usage_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    team_id BIGINT NULL,
    document_type_id BIGINT NOT NULL,
    processing_time_ms INT,
    success BOOLEAN NOT NULL,
    error_type VARCHAR(100) NULL,
    date DATE NOT NULL,
    hour TINYINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE SET NULL,
    FOREIGN KEY (document_type_id) REFERENCES document_types(id),
    INDEX idx_user_date (user_id, date),
    INDEX idx_date_hour (date, hour)
);

CREATE TABLE system_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metadata JSON,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_metric_name (metric_name),
    INDEX idx_recorded_at (recorded_at)
);
```

### Admin & Configuration

```sql
CREATE TABLE admin_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    key_name VARCHAR(100) NOT NULL UNIQUE,
    value TEXT,
    type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_key (key_name)
);

CREATE TABLE audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NULL,
    action VARCHAR(100) NOT NULL,
    model_type VARCHAR(100),
    model_id BIGINT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);
```

## Sample Data

### Default Document Types

```sql
INSERT INTO document_types (name, slug, description, credit_cost, supports_additional_file, validation_rules) VALUES
('National Identity Card', 'nic', 'Sri Lankan National Identity Card (front and back)', 1, true, '{"required_fields": ["nic_number", "full_name", "date_of_birth"]}'),
('Driving License', 'driving_license', 'Sri Lankan Driving License', 1, true, '{"required_fields": ["license_number", "full_name", "expiry_date"]}'),
('Vehicle Registration', 'vehicle_registration', 'Vehicle Registration Certificate', 2, false, '{"required_fields": ["registration_number", "vehicle_make", "vehicle_model"]}'),
('Land Deed', 'land_deed', 'Property Land Deed Document', 3, false, '{"required_fields": ["deed_number", "property_address", "owner_name"]}'),
('Invoice', 'invoice', 'Commercial Invoice Document', 1, false, '{"required_fields": ["invoice_number", "total_amount", "vendor_name"]}');
```

### Default Plans

```sql
INSERT INTO plans (name, slug, description, monthly_credits, price_lkr, features) VALUES
('Free', 'free', 'Perfect for trying out our service', 10, 0.00, '["Basic OCR", "Email Support"]'),
('Pro', 'pro', 'For small businesses and professionals', 500, 2500.00, '["Advanced OCR", "API Access", "Priority Support", "Team Collaboration"]'),
('Enterprise', 'enterprise', 'For large organizations', 2000, 8500.00, '["Premium OCR", "Custom Integration", "Dedicated Support", "Advanced Analytics"]');
```

## Migration Scripts

### Create Migration Files

```bash
php artisan make:migration create_document_types_table
php artisan make:migration create_documents_table
php artisan make:migration create_document_results_table
php artisan make:migration create_plans_table
php artisan make:migration create_user_plans_table
php artisan make:migration create_credit_transactions_table
php artisan make:migration create_payments_table
php artisan make:migration create_usage_analytics_table
php artisan make:migration create_admin_settings_table
php artisan make:migration create_audit_logs_table
php artisan make:migration seed_default_data
```

## Indexes Strategy

- **Primary Keys**: All tables use BIGINT auto-increment
- **Foreign Keys**: Proper cascading and null handling
- **Search Indexes**: User lookups, status filtering, date ranges
- **Composite Indexes**: User+date combinations for analytics
- **UUID Indexes**: For public document access

## Data Retention Policy

- **Documents**: Auto-delete after 24 hours (configurable)
- **Results**: Keep for 30 days for analytics
- **Audit Logs**: Retain for 1 year
- **Usage Analytics**: Aggregate and keep indefinitely