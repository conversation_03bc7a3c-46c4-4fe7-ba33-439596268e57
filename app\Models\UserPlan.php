<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Schema;

class UserPlan extends Model
{
    protected $fillable = [
        'user_id',
        'plan_id',
        'starts_at',
        'expires_at',
        'credits_remaining',
        'credits_used_this_month',
        'billing_cycle_start',
        'billing_cycle_end',
        'is_active',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'billing_cycle_start' => 'date',
        'billing_cycle_end' => 'date',
        'is_active' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCurrent($query)
    {
        $query = $query->where('is_active', true);

        // Check if the table has the new structure (expires_at) or old structure (billing_cycle_end)
        if (Schema::hasColumn('user_plans', 'expires_at')) {
            // New structure
            return $query->where(function($q) {
                $q->whereNull('expires_at')
                  ->orWhere('expires_at', '>', now());
            });
        } elseif (Schema::hasColumn('user_plans', 'billing_cycle_end')) {
            // Old structure - for backward compatibility
            return $query->where('billing_cycle_start', '<=', now())
                        ->where('billing_cycle_end', '>=', now());
        }

        // Fallback - just return active plans
        return $query;
    }

    public function isExpired(): bool
    {
        if (Schema::hasColumn('user_plans', 'expires_at') && $this->expires_at) {
            return $this->expires_at->isPast();
        } elseif (Schema::hasColumn('user_plans', 'billing_cycle_end') && $this->billing_cycle_end) {
            return $this->billing_cycle_end->isPast();
        }

        return false; // If no expiry date, never expires
    }

    public function isActive(): bool
    {
        return $this->is_active && !$this->isExpired();
    }
}
