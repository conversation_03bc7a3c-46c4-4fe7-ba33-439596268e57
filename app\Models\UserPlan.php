<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserPlan extends Model
{
    protected $fillable = [
        'user_id',
        'plan_id',
        'credits_remaining',
        'credits_used_this_month',
        'billing_cycle_start',
        'billing_cycle_end',
        'is_active',
    ];

    protected $casts = [
        'billing_cycle_start' => 'date',
        'billing_cycle_end' => 'date',
        'is_active' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCurrent($query)
    {
        return $query->where('is_active', true)
                    ->where('billing_cycle_start', '<=', now())
                    ->where('billing_cycle_end', '>=', now());
    }

    public function hasCreditsRemaining(): bool
    {
        return $this->credits_remaining > 0;
    }

    public function canUseCredits(int $amount): bool
    {
        return $this->credits_remaining >= $amount;
    }

    public function useCredits(int $amount): bool
    {
        if (!$this->canUseCredits($amount)) {
            return false;
        }

        $this->credits_remaining -= $amount;
        $this->credits_used_this_month += $amount;
        $this->save();

        return true;
    }

    public function getUsagePercentageAttribute(): float
    {
        $totalCredits = $this->plan->monthly_credits;
        if ($totalCredits == 0) {
            return 0;
        }

        return ($this->credits_used_this_month / $totalCredits) * 100;
    }
}
