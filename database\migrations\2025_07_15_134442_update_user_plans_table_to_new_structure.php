<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if we need to migrate from old structure to new structure
        if (Schema::hasColumn('user_plans', 'billing_cycle_start') && !Schema::hasColumn('user_plans', 'starts_at')) {
            Schema::table('user_plans', function (Blueprint $table) {
                // Add new columns
                $table->timestamp('starts_at')->after('plan_id')->nullable();
                $table->timestamp('expires_at')->after('starts_at')->nullable();
            });

            // Migrate existing data
            DB::statement("UPDATE user_plans SET starts_at = billing_cycle_start WHERE billing_cycle_start IS NOT NULL");
            DB::statement("UPDATE user_plans SET expires_at = billing_cycle_end WHERE billing_cycle_end IS NOT NULL");

            Schema::table('user_plans', function (Blueprint $table) {
                // Remove old columns after data migration
                $table->dropColumn([
                    'credits_remaining',
                    'credits_used_this_month',
                    'billing_cycle_start',
                    'billing_cycle_end'
                ]);
            });
        }

        // If table already has new structure, do nothing
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_plans', function (Blueprint $table) {
            // Add back old columns
            $table->integer('credits_remaining')->default(0)->after('plan_id');
            $table->integer('credits_used_this_month')->default(0)->after('credits_remaining');
            $table->date('billing_cycle_start')->after('credits_used_this_month');
            $table->date('billing_cycle_end')->after('billing_cycle_start');
        });

        // Migrate data back
        DB::statement("UPDATE user_plans SET billing_cycle_start = DATE(starts_at) WHERE starts_at IS NOT NULL");
        DB::statement("UPDATE user_plans SET billing_cycle_end = DATE(expires_at) WHERE expires_at IS NOT NULL");

        Schema::table('user_plans', function (Blueprint $table) {
            // Remove new columns
            $table->dropColumn(['starts_at', 'expires_at']);
        });
    }
};
