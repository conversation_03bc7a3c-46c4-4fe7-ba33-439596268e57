@extends('layouts.dashboard')

@section('title', 'User Management')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Company Management</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage companies, their teams, plans, and payments</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" class="flex items-center space-x-4">
                <div class="flex-1">
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Search users by name or email..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
                @if(request('search'))
                <a href="{{ route('admin.users') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition">
                    Clear
                </a>
                @endif
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan & Billing</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage & Credits</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API & Integration</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($users as $user)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span class="text-sm font-medium text-blue-600">{{ substr($user->name, 0, 1) }}</span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                @php
                                    $plan = $user->currentPlan;
                                    $isExpired = $plan && $plan->expires_at && $plan->expires_at->isPast();
                                    $isExpiringSoon = $plan && $plan->expires_at && $plan->expires_at->diffInDays() <= 7 && !$isExpired;
                                @endphp

                                <div class="flex items-center space-x-2 mb-2">
                                    @if($plan)
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full
                                            @if($isExpired) bg-red-100 text-red-800
                                            @elseif($isExpiringSoon) bg-yellow-100 text-yellow-800
                                            @elseif($plan->plan->slug === 'free') bg-gray-100 text-gray-800
                                            @else bg-green-100 text-green-800 @endif">
                                            {{ $plan->plan->name }}
                                        </span>
                                        @if($plan->plan->price_lkr > 0)
                                            <span class="text-xs text-gray-600">LKR {{ number_format($plan->plan->price_lkr, 0) }}/mo</span>
                                        @endif
                                    @else
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">No Plan</span>
                                    @endif
                                </div>

                                @if($plan && $plan->expires_at)
                                    <div class="text-xs text-gray-500">
                                        @if($isExpired)
                                            <span class="text-red-600 font-medium">⚠️ Expired {{ $plan->expires_at->diffForHumans() }}</span>
                                        @elseif($isExpiringSoon)
                                            <span class="text-yellow-600 font-medium">⏰ Expires in {{ $plan->expires_at->diffInDays() }} days</span>
                                        @else
                                            Expires {{ $plan->expires_at->format('M j, Y') }}
                                        @endif
                                    </div>
                                @elseif($plan)
                                    <div class="text-xs text-gray-500">Never expires</div>
                                @endif

                                <div class="mt-1">
                                    <button onclick="showBillingModal({{ $user->id }})" class="text-xs text-blue-600 hover:text-blue-800">
                                        💳 Manage Billing
                                    </button>
                                </div>
                            </td>

                            <td class="px-6 py-4">
                                <div class="space-y-1">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm font-medium text-gray-900">{{ number_format($user->remaining_credits) }}</span>
                                        <span class="text-xs text-gray-500">credits left</span>
                                    </div>

                                    @if($plan)
                                        @php
                                            $totalCredits = $plan->plan->monthly_credits;
                                            $usedCredits = $totalCredits - $user->remaining_credits;
                                            $usagePercent = $totalCredits > 0 ? ($usedCredits / $totalCredits) * 100 : 0;
                                        @endphp
                                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                                            <div class="bg-blue-600 h-1.5 rounded-full" style="width: {{ min(100, $usagePercent) }}%"></div>
                                        </div>
                                        <div class="text-xs text-gray-500">{{ number_format($usedCredits) }}/{{ number_format($totalCredits) }} used</div>
                                    @endif

                                    <div class="text-xs text-gray-500">
                                        📄 {{ number_format($user->documents->count()) }} total docs
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        📅 {{ number_format($user->documents->where('created_at', '>=', now()->subDays(30))->count()) }} this month
                                    </div>
                                </div>
                            </td>

                            <td class="px-6 py-4">
                                <div class="space-y-1">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm font-medium text-gray-900">{{ $user->tokens->count() }}</span>
                                        <span class="text-xs text-gray-500">API tokens</span>
                                    </div>

                                    @php
                                        $lastUsedToken = $user->tokens->where('last_used_at', '!=', null)->sortByDesc('last_used_at')->first();
                                    @endphp

                                    @if($lastUsedToken)
                                        <div class="text-xs text-green-600">
                                            🟢 Last used {{ $lastUsedToken->last_used_at->diffForHumans() }}
                                        </div>
                                    @elseif($user->tokens->count() > 0)
                                        <div class="text-xs text-yellow-600">
                                            🟡 Tokens created, never used
                                        </div>
                                    @else
                                        <div class="text-xs text-gray-500">
                                            ⚪ No API integration
                                        </div>
                                    @endif

                                    <div class="text-xs text-gray-500">
                                        👥 {{ $user->teams->count() }} team(s)
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($user->email_verified_at)
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Unverified
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $user->created_at->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-1">
                                    <button class="text-blue-600 hover:text-blue-900 p-1" onclick="viewCompanyDetails({{ $user->id }})" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900 p-1" onclick="managePlan({{ $user->id }})" title="Manage Plan">
                                        <i class="fas fa-credit-card"></i>
                                    </button>
                                    <button class="text-purple-600 hover:text-purple-900 p-1" onclick="addPayment({{ $user->id }})" title="Add Payment">
                                        <i class="fas fa-dollar-sign"></i>
                                    </button>
                                    <button class="text-orange-600 hover:text-orange-900 p-1" onclick="editCredits({{ $user->id }})" title="Edit Credits">
                                        <i class="fas fa-coins"></i>
                                    </button>
                                    <button class="text-teal-600 hover:text-teal-900 p-1" onclick="viewUsageLogs({{ $user->id }})" title="Usage Logs">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                    @if($user->email !== '<EMAIL>')
                                    <button class="text-red-600 hover:text-red-900 p-1" onclick="suspendUser({{ $user->id }})" title="Suspend">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $users->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Billing Management Modal -->
<div id="billingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Billing Management</h3>
                <button onclick="hideBillingModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div id="billingContent">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function showBillingModal(userId) {
    document.getElementById('billingModal').classList.remove('hidden');

    // Load billing information
    document.getElementById('billingContent').innerHTML = `
        <div class="space-y-4">
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-2">Current Plan Status</h4>
                <div class="text-sm text-blue-800">Loading...</div>
            </div>

            <div class="space-y-3">
                <h4 class="font-medium text-gray-900">Quick Actions</h4>

                <button onclick="extendPlan(${userId}, 1)" class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">
                    Extend Plan by 1 Month
                </button>

                <button onclick="addCredits(${userId})" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                    Add Credits
                </button>

                <button onclick="changePlan(${userId})" class="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700">
                    Change Plan
                </button>

                <button onclick="recordPayment(${userId})" class="w-full bg-yellow-600 text-white py-2 px-4 rounded hover:bg-yellow-700">
                    Record Offline Payment
                </button>
            </div>

            <div class="border-t pt-3">
                <h4 class="font-medium text-gray-900 mb-2">Payment History</h4>
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between py-1">
                        <span>Last payment:</span>
                        <span>Loading...</span>
                    </div>
                    <div class="flex justify-between py-1">
                        <span>Total paid:</span>
                        <span>Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function hideBillingModal() {
    document.getElementById('billingModal').classList.add('hidden');
}

function extendPlan(userId, months) {
    if (confirm(\`Extend plan by \${months} month(s)?\`)) {
        // Make AJAX request to extend plan
        alert('Plan extended successfully');
        hideBillingModal();
        location.reload();
    }
}

function addCredits(userId) {
    const credits = prompt('How many credits to add?');
    if (credits && !isNaN(credits)) {
        editCredits(userId, parseInt(credits));
    }
}

function changePlan(userId) {
    const plans = ['free', 'basic', 'professional', 'enterprise'];
    const selectedPlan = prompt('Enter plan slug (free, basic, professional, enterprise):');
    if (plans.includes(selectedPlan)) {
        alert('Plan changed to: ' + selectedPlan);
        hideBillingModal();
        location.reload();
    }
}

function recordPayment(userId) {
    const amount = prompt('Payment amount (LKR):');
    if (amount && !isNaN(amount)) {
        const months = prompt('Extend plan by how many months?', '1');
        if (months && !isNaN(months)) {
            alert(\`Payment of LKR \${amount} recorded. Plan extended by \${months} month(s).\`);
            hideBillingModal();
            location.reload();
        }
    }
}

function viewCompanyDetails(userId) {
    // Implement company details modal with team members, usage stats, etc.
    alert('View company details for ID: ' + userId + '\n\nFeatures:\n- Team members list\n- Document processing history\n- API usage statistics\n- Payment history');
}

function managePlan(userId) {
    // Implement plan management modal
    alert('Manage plan for company ID: ' + userId + '\n\nFeatures:\n- Change plan type\n- Set expiry date\n- Enable/disable features\n- Plan upgrade/downgrade');
}

function addPayment(userId) {
    // Implement payment recording modal
    alert('Add offline payment for company ID: ' + userId + '\n\nFeatures:\n- Record payment amount\n- Set payment date\n- Extend plan duration\n- Add payment notes');
}

function editCredits(userId) {
    // Implement credit editing modal
    const credits = prompt('Enter new credit amount:');
    if (credits !== null && !isNaN(credits)) {
        // Make AJAX request to update credits
        fetch(`/admin/users/${userId}/credits`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ credits: parseInt(credits) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Credits updated successfully');
                location.reload();
            } else {
                alert('Error updating credits: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error updating credits');
        });
    }
}

function viewUsageLogs(userId) {
    // Implement usage logs modal
    alert('View usage logs for company ID: ' + userId + '\n\nFeatures:\n- Document processing history\n- API call logs\n- Credit usage timeline\n- Export usage reports');
}

function suspendUser(userId) {
    if (confirm('Are you sure you want to suspend this company? This will disable their access and API tokens.')) {
        // Make AJAX request to suspend user
        fetch(`/admin/users/${userId}/suspend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Company suspended successfully');
                location.reload();
            } else {
                alert('Error suspending company: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error suspending company');
        });
    }
}
</script>
@endpush
@endsection
