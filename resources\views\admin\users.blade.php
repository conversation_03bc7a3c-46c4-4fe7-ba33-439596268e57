@extends('layouts.dashboard')

@section('title', 'User Management')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Company Management</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage companies, their teams, plans, and payments</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" class="flex items-center space-x-4">
                <div class="flex-1">
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Search users by name or email..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
                @if(request('search'))
                <a href="{{ route('admin.users') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition">
                    Clear
                </a>
                @endif
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Team Info</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan & Payment</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API Tokens</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($users as $user)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span class="text-sm font-medium text-blue-600">{{ substr($user->name, 0, 1) }}</span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $user->currentTeam->name ?? 'Personal Account' }}</div>
                                <div class="text-sm text-gray-500">{{ $user->teams->count() }} team(s) • {{ $user->ownedTeams->count() }} owned</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($user->currentPlan)
                                    <div class="text-sm font-medium text-gray-900">{{ $user->currentPlan->plan->name }}</div>
                                    <div class="text-xs text-gray-500">
                                        @if($user->currentPlan->expires_at)
                                            @if($user->currentPlan->expires_at->isPast())
                                                <span class="text-red-600">Expired {{ $user->currentPlan->expires_at->diffForHumans() }}</span>
                                            @else
                                                Expires {{ $user->currentPlan->expires_at->diffForHumans() }}
                                            @endif
                                        @else
                                            Lifetime Plan
                                        @endif
                                    </div>
                                    <div class="text-xs text-blue-600">
                                        ${{ number_format($user->currentPlan->plan->price ?? 0, 2) }}/month
                                    </div>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        No Active Plan
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ number_format($user->remaining_credits) }} credits</div>
                                <div class="text-xs text-gray-500">{{ number_format($user->documents->count()) }} docs processed</div>
                                <div class="text-xs text-gray-500">{{ number_format($user->documents->where('created_at', '>=', now()->subDays(30))->count()) }} this month</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $user->tokens->count() }} active</div>
                                <div class="text-xs text-gray-500">
                                    Last used: {{ $user->tokens->where('last_used_at', '!=', null)->sortByDesc('last_used_at')->first()?->last_used_at?->diffForHumans() ?? 'Never' }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($user->email_verified_at)
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Unverified
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $user->created_at->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-1">
                                    <button class="text-blue-600 hover:text-blue-900 p-1" onclick="viewCompanyDetails({{ $user->id }})" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900 p-1" onclick="managePlan({{ $user->id }})" title="Manage Plan">
                                        <i class="fas fa-credit-card"></i>
                                    </button>
                                    <button class="text-purple-600 hover:text-purple-900 p-1" onclick="addPayment({{ $user->id }})" title="Add Payment">
                                        <i class="fas fa-dollar-sign"></i>
                                    </button>
                                    <button class="text-orange-600 hover:text-orange-900 p-1" onclick="editCredits({{ $user->id }})" title="Edit Credits">
                                        <i class="fas fa-coins"></i>
                                    </button>
                                    <button class="text-teal-600 hover:text-teal-900 p-1" onclick="viewUsageLogs({{ $user->id }})" title="Usage Logs">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                    @if($user->email !== '<EMAIL>')
                                    <button class="text-red-600 hover:text-red-900 p-1" onclick="suspendUser({{ $user->id }})" title="Suspend">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $users->links() }}
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function viewCompanyDetails(userId) {
    // Implement company details modal with team members, usage stats, etc.
    alert('View company details for ID: ' + userId + '\n\nFeatures:\n- Team members list\n- Document processing history\n- API usage statistics\n- Payment history');
}

function managePlan(userId) {
    // Implement plan management modal
    alert('Manage plan for company ID: ' + userId + '\n\nFeatures:\n- Change plan type\n- Set expiry date\n- Enable/disable features\n- Plan upgrade/downgrade');
}

function addPayment(userId) {
    // Implement payment recording modal
    alert('Add offline payment for company ID: ' + userId + '\n\nFeatures:\n- Record payment amount\n- Set payment date\n- Extend plan duration\n- Add payment notes');
}

function editCredits(userId) {
    // Implement credit editing modal
    const credits = prompt('Enter new credit amount:');
    if (credits !== null && !isNaN(credits)) {
        // Make AJAX request to update credits
        fetch(`/admin/users/${userId}/credits`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ credits: parseInt(credits) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Credits updated successfully');
                location.reload();
            } else {
                alert('Error updating credits: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error updating credits');
        });
    }
}

function viewUsageLogs(userId) {
    // Implement usage logs modal
    alert('View usage logs for company ID: ' + userId + '\n\nFeatures:\n- Document processing history\n- API call logs\n- Credit usage timeline\n- Export usage reports');
}

function suspendUser(userId) {
    if (confirm('Are you sure you want to suspend this company? This will disable their access and API tokens.')) {
        // Make AJAX request to suspend user
        fetch(`/admin/users/${userId}/suspend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Company suspended successfully');
                location.reload();
            } else {
                alert('Error suspending company: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error suspending company');
        });
    }
}
</script>
@endpush
@endsection
