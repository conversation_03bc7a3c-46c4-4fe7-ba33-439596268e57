<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\DocumentType;
use App\Services\DocumentFieldsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class ExportController extends Controller
{
    protected $documentFieldsService;

    public function __construct(DocumentFieldsService $documentFieldsService)
    {
        $this->documentFieldsService = $documentFieldsService;
    }

    public function exportDocuments(Request $request)
    {
        $request->validate([
            'document_type' => 'nullable|string|exists:document_types,slug',
            'status' => 'nullable|string|in:pending,processing,completed,failed',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date',
            'format' => 'nullable|string|in:csv,excel',
        ]);

        $user = $request->user();
        $format = $request->get('format', 'csv');

        // Build query
        $query = $user->documents()
            ->with(['documentType', 'result'])
            ->where('status', 'completed'); // Only export completed documents

        if ($request->document_type) {
            $query->whereHas('documentType', function($q) use ($request) {
                $q->where('slug', $request->document_type);
            });
        }

        if ($request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $documents = $query->orderBy('created_at', 'desc')->get();

        if ($documents->isEmpty()) {
            return back()->with('error', 'No documents found for export.');
        }

        // Group documents by type for separate exports
        $documentsByType = $documents->groupBy('documentType.slug');

        if ($documentsByType->count() === 1) {
            // Single document type - export directly
            $documentType = $documentsByType->keys()->first();
            return $this->exportSingleType($documentType, $documentsByType[$documentType], $format);
        } else {
            // Multiple document types - create zip file
            return $this->exportMultipleTypes($documentsByType, $format);
        }
    }

    private function exportSingleType($documentType, $documents, $format)
    {
        $headers = $this->documentFieldsService->getExportHeaders($documentType);
        $data = [];

        foreach ($documents as $document) {
            $extractedData = $document->result ? $document->result->extracted_data : [];
            $metadata = [
                'filename' => $document->original_filename,
                'processed_date' => $document->processing_completed_at ? $document->processing_completed_at->format('Y-m-d H:i:s') : '',
                'confidence_score' => $document->confidence_score ? number_format($document->confidence_score * 100, 1) . '%' : '',
            ];

            $formattedData = $this->documentFieldsService->formatForExport($documentType, $extractedData, $metadata);
            $data[] = $formattedData;
        }

        $filename = $this->documentFieldsService->getDocumentTypeName($documentType) . '_Export_' . date('Y-m-d_H-i-s');

        if ($format === 'csv') {
            return $this->generateCsv($data, $headers, $filename);
        } else {
            return $this->generateExcel($data, $headers, $filename);
        }
    }

    private function exportMultipleTypes($documentsByType, $format)
    {
        // For now, just export the first type
        // TODO: Implement zip file creation for multiple types
        $firstType = $documentsByType->keys()->first();
        return $this->exportSingleType($firstType, $documentsByType[$firstType], $format);
    }

    private function generateCsv($data, $headers, $filename)
    {
        $csvData = [];

        // Add headers
        $csvData[] = array_values($headers);

        // Add data rows
        foreach ($data as $row) {
            $csvRow = [];
            foreach (array_keys($headers) as $field) {
                $csvRow[] = $row[$field] ?? '';
            }
            $csvData[] = $csvRow;
        }

        $output = fopen('php://temp', 'w');
        foreach ($csvData as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);

        return Response::make($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '.csv"',
        ]);
    }

    private function generateExcel($data, $headers, $filename)
    {
        // For now, fallback to CSV
        // TODO: Implement Excel export using PhpSpreadsheet
        return $this->generateCsv($data, $headers, $filename);
    }

    public function exportTemplate(Request $request)
    {
        $request->validate([
            'document_type' => 'required|string|exists:document_types,slug',
        ]);

        $documentType = $request->document_type;
        $headers = $this->documentFieldsService->getExportHeaders($documentType);
        $sampleData = $this->documentFieldsService->getSampleData($documentType);

        // Add metadata to sample
        $sampleData['document_filename'] = 'sample_document.pdf';
        $sampleData['processed_date'] = now()->format('Y-m-d H:i:s');
        $sampleData['confidence_score'] = '95.5%';

        $formattedSample = $this->documentFieldsService->formatForExport($documentType, $sampleData, [
            'filename' => 'sample_document.pdf',
            'processed_date' => now()->format('Y-m-d H:i:s'),
            'confidence_score' => '95.5%',
        ]);

        $data = [$formattedSample];
        $filename = $this->documentFieldsService->getDocumentTypeName($documentType) . '_Template';

        return $this->generateCsv($data, $headers, $filename);
    }
}
