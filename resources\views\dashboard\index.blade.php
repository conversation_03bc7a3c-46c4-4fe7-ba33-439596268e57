@extends('layouts.dashboard')

@section('title', 'Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Welcome back, {{ $user->name }}!</h1>
                    <p class="mt-1 text-sm text-gray-600">Here's what's happening with your documents today.</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('dashboard.upload') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        <i class="fas fa-upload mr-2"></i>
                        Upload Document
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Documents -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-file-alt text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Documents</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($totalDocuments) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Completed Documents -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-check-circle text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($completedDocuments) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Documents -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($pendingDocuments) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Credits Remaining -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-coins text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Credits Remaining</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                @if($currentPlan)
                                    {{ number_format($currentPlan->credits_remaining) }}
                                @else
                                    <span class="text-red-500">No Plan</span>
                                @endif
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Documents -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Documents</h3>
                    <a href="{{ route('dashboard.documents') }}" class="text-sm text-blue-600 hover:text-blue-500">View all</a>
                </div>
                
                @if($recentDocuments->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentDocuments as $document)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    @if($document->status === 'completed')
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-green-600 text-sm"></i>
                                        </div>
                                    @elseif($document->status === 'failed')
                                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-times text-red-600 text-sm"></i>
                                        </div>
                                    @elseif($document->status === 'processing')
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-spinner fa-spin text-blue-600 text-sm"></i>
                                        </div>
                                    @else
                                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-clock text-yellow-600 text-sm"></i>
                                        </div>
                                    @endif
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $document->original_filename }}</p>
                                    <p class="text-xs text-gray-500">{{ $document->documentType->name }} • {{ $document->created_at->diffForHumans() }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($document->status === 'completed') bg-green-100 text-green-800
                                    @elseif($document->status === 'failed') bg-red-100 text-red-800
                                    @elseif($document->status === 'processing') bg-blue-100 text-blue-800
                                    @else bg-yellow-100 text-yellow-800 @endif">
                                    {{ ucfirst($document->status) }}
                                </span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-6">
                        <i class="fas fa-file-alt text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No documents yet</p>
                        <a href="{{ route('dashboard.upload') }}" class="text-blue-600 hover:text-blue-500 text-sm">Upload your first document</a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Current Plan -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Current Plan</h3>
                
                @if($currentPlan)
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-2xl font-bold text-blue-600">{{ $currentPlan->plan->name }}</span>
                            <span class="text-lg font-semibold text-gray-900">{{ $currentPlan->plan->formatted_price }}</span>
                        </div>
                        
                        <div class="space-y-3">
                            <!-- Credits Usage -->
                            <div>
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>Credits Used</span>
                                    <span>{{ number_format($currentPlan->credits_used_this_month) }} / {{ number_format($currentPlan->plan->monthly_credits) }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ min(100, $currentPlan->usage_percentage) }}%"></div>
                                </div>
                            </div>
                            
                            <!-- Billing Cycle -->
                            <div class="text-sm text-gray-600">
                                <p>Billing cycle ends: {{ $currentPlan->billing_cycle_end->format('M j, Y') }}</p>
                            </div>
                            
                            <!-- Features -->
                            @if($currentPlan->plan->features)
                                <div>
                                    <p class="text-sm font-medium text-gray-700 mb-2">Plan Features:</p>
                                    <ul class="text-sm text-gray-600 space-y-1">
                                        @foreach($currentPlan->plan->features as $feature)
                                            <li class="flex items-center">
                                                <i class="fas fa-check text-green-500 mr-2 text-xs"></i>
                                                {{ $feature }}
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                        </div>
                        
                        @if($currentPlan->plan->isFree())
                            <div class="mt-4">
                                <a href="#" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition text-center block">
                                    Upgrade Plan
                                </a>
                            </div>
                        @endif
                    </div>
                @else
                    <div class="text-center py-6">
                        <i class="fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"></i>
                        <p class="text-gray-700 mb-4">No active plan found</p>
                        <a href="#" class="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition">
                            Choose a Plan
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                <a href="{{ route('dashboard.upload') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition">
                    <div class="flex-shrink-0">
                        <i class="fas fa-upload text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-blue-900">Upload Document</p>
                        <p class="text-xs text-blue-700">Process new documents</p>
                    </div>
                </a>
                
                <a href="{{ route('dashboard.documents') }}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition">
                    <div class="flex-shrink-0">
                        <i class="fas fa-file-alt text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-900">View Documents</p>
                        <p class="text-xs text-green-700">Browse all documents</p>
                    </div>
                </a>
                
                <a href="{{ route('dashboard.usage') }}" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-bar text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-purple-900">Usage Analytics</p>
                        <p class="text-xs text-purple-700">View usage statistics</p>
                    </div>
                </a>
                
                <a href="{{ route('profile.show') }}" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition">
                    <div class="flex-shrink-0">
                        <i class="fas fa-key text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-orange-900">API Tokens</p>
                        <p class="text-xs text-orange-700">Manage API access</p>
                    </div>
                </a>

                <a href="{{ url('/api/documentation') }}" target="_blank" class="flex items-center p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition">
                    <div class="flex-shrink-0">
                        <i class="fas fa-code text-indigo-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-indigo-900">API Documentation</p>
                        <p class="text-xs text-indigo-700">Swagger/OpenAPI docs</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
