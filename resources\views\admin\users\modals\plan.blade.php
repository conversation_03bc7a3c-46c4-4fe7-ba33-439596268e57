<!-- Plan Management Modal -->
<div id="planModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Manage Plan</h3>
                <button onclick="hidePlanModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="planForm" class="space-y-4">
                @csrf
                <input type="hidden" id="plan_user_id" name="user_id">
                
                <div>
                    <label for="plan_slug" class="block text-sm font-medium text-gray-700 mb-1">Plan</label>
                    <select id="plan_slug" name="plan_slug" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        @foreach($plans ?? [] as $plan)
                            <option value="{{ $plan->slug }}" data-price="{{ $plan->price_lkr }}">
                                {{ $plan->name }} 
                                @if($plan->price_lkr > 0)
                                    (LKR {{ number_format($plan->price_lkr, 0) }}/month)
                                @else
                                    (Free)
                                @endif
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label for="duration_months" class="block text-sm font-medium text-gray-700 mb-1">Duration (Months)</label>
                    <select id="duration_months" name="duration_months" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="1">1 Month</option>
                        <option value="3">3 Months</option>
                        <option value="6">6 Months</option>
                        <option value="12">12 Months</option>
                        <option value="24">24 Months</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Free plan never expires regardless of duration</p>
                </div>
                
                <div>
                    <label for="plan_reason" class="block text-sm font-medium text-gray-700 mb-1">Reason (Optional)</label>
                    <textarea id="plan_reason" name="reason" rows="3" 
                              placeholder="e.g., Customer upgrade request, promotional offer"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                
                <div id="plan_summary" class="bg-blue-50 border border-blue-200 rounded-md p-3 hidden">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Plan Summary</h4>
                    <div class="text-sm text-blue-800">
                        <div class="flex justify-between">
                            <span>Plan:</span>
                            <span id="summary_plan_name"></span>
                        </div>
                        <div class="flex justify-between">
                            <span>Duration:</span>
                            <span id="summary_duration"></span>
                        </div>
                        <div class="flex justify-between">
                            <span>Expires:</span>
                            <span id="summary_expires"></span>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hidePlanModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700">
                        <i class="fas fa-save mr-2"></i>
                        Update Plan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
