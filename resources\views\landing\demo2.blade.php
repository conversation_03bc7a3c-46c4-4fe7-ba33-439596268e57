<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Real OCR Demo - SmartOCR.lk</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('landing.index') }}" class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-blue-600">SmartOCR.lk</h1>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ route('landing.index') }}" class="text-gray-700 hover:text-blue-600">Home</a>
                    <a href="{{ route('landing.demo') }}" class="text-gray-700 hover:text-blue-600">Demo</a>
                    <span class="text-blue-600 font-medium">Real OCR Demo</span>
                    @auth
                        <a href="{{ route('dashboard') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Dashboard</a>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600">Login</a>
                        <a href="{{ route('register') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Get Started</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Demo Section -->
    <section class="py-12">
        <div class="max-w-4xl mx-auto px-4">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Real OCR Demo</h1>
                <p class="text-xl text-gray-600 mb-4">Experience our actual OCR processing with real API integration</p>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-yellow-600 mr-2"></i>
                        <p class="text-sm text-yellow-800">
                            <strong>Demo Limit:</strong> 1 document per IP address per day.
                            <a href="{{ route('register') }}" class="underline hover:text-yellow-900">Register for unlimited access</a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Demo Limit Status -->
            <div id="limitStatus" class="mb-6 hidden">
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                        <div>
                            <p class="text-sm text-red-800 font-medium">Demo limit reached</p>
                            <p class="text-xs text-red-700">You can try again tomorrow or <a href="{{ route('register') }}" class="underline">register for unlimited access</a></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Demo Form -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <form id="demoForm" enctype="multipart/form-data" class="space-y-6">
                    @csrf

                    <!-- Document Type Selection -->
                    <div>
                        <label for="document_type" class="block text-sm font-medium text-gray-700 mb-2">
                            Document Type *
                        </label>
                        <select id="document_type" name="document_type" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select document type...</option>
                            @foreach($documentTypes as $type)
                                @if(in_array($type->slug, ['nic', 'driving_license', 'vehicle_registration']))
                                <option value="{{ $type->slug }}"
                                        data-supports-additional="{{ $type->supports_additional_file ? 'true' : 'false' }}"
                                        data-max-size="{{ $type->max_file_size_mb }}"
                                        data-extensions="{{ implode(',', $type->allowed_extensions) }}">
                                    {{ $type->name }}
                                </option>
                                @endif
                            @endforeach
                        </select>

                        <div id="documentTypeInfo" class="mt-2 hidden">
                            <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-blue-700">
                                            <span id="typeDescription"></span>
                                        </p>
                                        <div class="mt-2 text-xs text-blue-600">
                                            <span>Max file size: <strong id="maxFileSize">-</strong></span> |
                                            <span>Allowed formats: <strong id="allowedFormats">-</strong></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main File Upload -->
                    <div>
                        <label for="file" class="block text-sm font-medium text-gray-700 mb-2">
                            Main Document *
                        </label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition">
                            <input type="file" id="file" name="file" accept=".jpg,.jpeg,.png,.pdf" required class="hidden">
                            <div id="fileDropZone" class="cursor-pointer">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-lg text-gray-600 mb-2">Click to upload or drag and drop</p>
                                <p class="text-sm text-gray-500">JPG, PNG, PDF up to <span id="maxSizeDisplay">10MB</span></p>
                            </div>
                            <div id="filePreview" class="hidden">
                                <div class="flex items-center justify-center space-x-3">
                                    <i class="fas fa-file text-blue-500 text-2xl"></i>
                                    <div class="text-left">
                                        <p id="fileName" class="text-gray-700 font-medium"></p>
                                        <p id="fileSize" class="text-sm text-gray-500"></p>
                                    </div>
                                    <button type="button" id="removeFile" class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times text-xl"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional File Upload -->
                    <div id="additionalFileSection" class="hidden">
                        <label for="additional_file" class="block text-sm font-medium text-gray-700 mb-2">
                            Additional File (Back side, etc.)
                        </label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition">
                            <input type="file" id="additional_file" name="additional_file" accept=".jpg,.jpeg,.png,.pdf" class="hidden">
                            <div id="additionalFileDropZone" class="cursor-pointer">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600 mb-1">Upload additional file (optional)</p>
                                <p class="text-sm text-gray-500">JPG, PNG, PDF up to <span id="additionalMaxSizeDisplay">10MB</span></p>
                            </div>
                            <div id="additionalFilePreview" class="hidden">
                                <div class="flex items-center justify-center space-x-3">
                                    <i class="fas fa-file text-blue-500 text-xl"></i>
                                    <div class="text-left">
                                        <p id="additionalFileName" class="text-gray-700 font-medium"></p>
                                        <p id="additionalFileSize" class="text-sm text-gray-500"></p>
                                    </div>
                                    <button type="button" id="removeAdditionalFile" class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center pt-4">
                        <button type="submit" id="submitBtn"
                                class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition disabled:bg-gray-400 disabled:cursor-not-allowed">
                            <span id="submitText">Process with Real OCR</span>
                            <i id="submitSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                        </button>
                        <p class="text-xs text-gray-500 mt-2">This will use our actual OCR API service</p>
                    </div>
                </form>
            </div>

            <!-- Results Section -->
            <div id="resultsSection" class="mt-8 hidden">
                <!-- Processing Status -->
                <div id="processingStatus" class="bg-white rounded-lg shadow-lg p-8 text-center">
                    <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Processing Document</h3>
                    <p class="text-gray-600">Please wait while we analyze your document with real OCR...</p>
                </div>

                <!-- Error Message -->
                <div id="errorMessage" class="bg-white rounded-lg shadow-lg p-8 text-center hidden">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Processing Failed</h3>
                    <p id="errorText" class="text-gray-600 mb-4"></p>
                    <button onclick="resetForm()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        Try Again
                    </button>
                </div>

                <!-- Extracted Data -->
                <div id="extractedData" class="bg-white rounded-lg shadow-lg p-8 hidden">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Extraction Results</h3>
                        <div class="flex items-center space-x-4 text-sm text-gray-600">
                            <span>Confidence: <strong id="confidenceScore">-</strong></span>
                            <span>Time: <strong id="processingTime">-</strong></span>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4 mb-6">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-file-alt text-blue-600 mr-2"></i>
                            <span class="text-sm font-medium text-gray-700">Document Type: <span id="documentType" class="text-gray-900">-</span></span>
                        </div>
                    </div>

                    <div id="dataFields" class="space-y-3">
                        <!-- Dynamic content will be inserted here -->
                    </div>

                    <div class="mt-6 pt-6 border-t border-gray-200 text-center">
                        <p class="text-sm text-gray-600 mb-4">Want to process more documents?</p>
                        <a href="{{ route('register') }}" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition">
                            Register for Free Account
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">SmartOCR.lk</h3>
                    <p class="text-gray-400">Intelligent document processing for Sri Lankan businesses.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="{{ route('landing.index') }}#features" class="hover:text-white">Features</a></li>
                        <li><a href="{{ route('landing.index') }}#pricing" class="hover:text-white">Pricing</a></li>
                        <li><a href="{{ route('landing.demo') }}" class="hover:text-white">Demo</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="{{ route('landing.contact') }}" class="hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white">Documentation</a></li>
                        <li><a href="#" class="hover:text-white">API Reference</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-white">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; {{ date('Y') }} SmartOCR.lk. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkDemoLimit();
            setupForm();
        });

        function checkDemoLimit() {
            fetch('{{ route("demo2.check-limit") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.limit_reached) {
                        document.getElementById('limitStatus').classList.remove('hidden');
                        document.getElementById('submitBtn').disabled = true;
                        document.getElementById('submitText').textContent = 'Demo Limit Reached';
                    }
                })
                .catch(error => console.error('Error checking demo limit:', error));
        }

        function setupForm() {
            const form = document.getElementById('demoForm');
            const documentTypeSelect = document.getElementById('document_type');
            const additionalFileSection = document.getElementById('additionalFileSection');
            const documentTypeInfo = document.getElementById('documentTypeInfo');

            // Handle document type change
            documentTypeSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];

                if (this.value) {
                    const supportsAdditional = selectedOption.dataset.supportsAdditional === 'true';
                    const maxSize = selectedOption.dataset.maxSize;
                    const extensions = selectedOption.dataset.extensions;

                    // Show/hide additional file section
                    if (supportsAdditional) {
                        additionalFileSection.classList.remove('hidden');
                    } else {
                        additionalFileSection.classList.add('hidden');
                        document.getElementById('additional_file').value = '';
                        resetFilePreview('additional');
                    }

                    // Update info
                    document.getElementById('maxFileSize').textContent = maxSize + 'MB';
                    document.getElementById('allowedFormats').textContent = extensions.toUpperCase();
                    document.getElementById('maxSizeDisplay').textContent = maxSize + 'MB';
                    document.getElementById('additionalMaxSizeDisplay').textContent = maxSize + 'MB';

                    documentTypeInfo.classList.remove('hidden');
                } else {
                    additionalFileSection.classList.add('hidden');
                    documentTypeInfo.classList.add('hidden');
                }
            });

            // File upload handlers
            setupFileUpload('file', 'fileDropZone', 'filePreview', 'fileName', 'fileSize', 'removeFile');
            setupFileUpload('additional_file', 'additionalFileDropZone', 'additionalFilePreview', 'additionalFileName', 'additionalFileSize', 'removeAdditionalFile');

            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                processDemo();
            });
        }

        function processDemo() {
            const form = document.getElementById('demoForm');
            const formData = new FormData(form);

            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const submitSpinner = document.getElementById('submitSpinner');
            const resultsSection = document.getElementById('resultsSection');
            const processingStatus = document.getElementById('processingStatus');
            const extractedData = document.getElementById('extractedData');
            const errorMessage = document.getElementById('errorMessage');

            // Show processing state
            submitBtn.disabled = true;
            submitText.textContent = 'Processing...';
            submitSpinner.classList.remove('hidden');
            resultsSection.classList.remove('hidden');
            processingStatus.classList.remove('hidden');
            extractedData.classList.add('hidden');
            errorMessage.classList.add('hidden');

            // Make actual API call to demo endpoint
            fetch('{{ route("demo2.process") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                // Hide processing
                processingStatus.classList.add('hidden');

                if (data.success) {
                    // Show results
                    extractedData.classList.remove('hidden');
                    showDemoResults(data.data);
                } else {
                    // Show error
                    errorMessage.classList.remove('hidden');
                    document.getElementById('errorText').textContent = data.error.message;

                    // Check if limit exceeded
                    if (data.error.code === 'DEMO_LIMIT_EXCEEDED') {
                        document.getElementById('limitStatus').classList.remove('hidden');
                    }
                }

                // Reset button
                submitBtn.disabled = false;
                submitText.textContent = 'Process Another Document';
                submitSpinner.classList.add('hidden');
            })
            .catch(error => {
                console.error('Error:', error);

                // Hide processing, show error
                processingStatus.classList.add('hidden');
                errorMessage.classList.remove('hidden');
                document.getElementById('errorText').textContent = 'An error occurred while processing the document.';

                // Reset button
                submitBtn.disabled = false;
                submitText.textContent = 'Try Again';
                submitSpinner.classList.add('hidden');
            });
        }

        function showDemoResults(responseData) {
            const dataFields = document.getElementById('dataFields');
            const confidenceScore = document.getElementById('confidenceScore');
            const processingTime = document.getElementById('processingTime');
            const documentTypeSpan = document.getElementById('documentType');

            // Use actual response data
            const data = responseData.result;

            // Populate fields
            dataFields.innerHTML = '';
            Object.entries(data).forEach(([key, value]) => {
                if (key === 'raw_text' || key === 'raw_response') return; // Skip raw data

                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'flex justify-between py-2 border-b border-gray-200';

                // Handle array values
                const displayValue = Array.isArray(value) ? value.join(', ') : value;

                fieldDiv.innerHTML = `
                    <span class="text-gray-600">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span>
                    <span class="font-medium">${displayValue}</span>
                `;
                dataFields.appendChild(fieldDiv);
            });

            // Set processing details from response
            confidenceScore.textContent = (responseData.confidence_score * 100).toFixed(1) + '%';
            processingTime.textContent = (responseData.processing_time_ms / 1000).toFixed(2) + 's';
            documentTypeSpan.textContent = responseData.document_type.replace('_', ' ').toUpperCase();

            // Show demo notice if applicable
            if (responseData.demo_mode) {
                const demoNotice = document.createElement('div');
                demoNotice.className = 'mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md';
                demoNotice.innerHTML = `
                    <div class="flex">
                        <i class="fas fa-info-circle text-blue-400 mr-2 mt-0.5"></i>
                        <p class="text-sm text-blue-700">${responseData.message}</p>
                    </div>
                `;
                dataFields.appendChild(demoNotice);
            }
        }

        function setupFileUpload(inputId, dropZoneId, previewId, fileNameId, fileSizeId, removeId) {
            const input = document.getElementById(inputId);
            const dropZone = document.getElementById(dropZoneId);
            const preview = document.getElementById(previewId);
            const fileName = document.getElementById(fileNameId);
            const fileSize = document.getElementById(fileSizeId);
            const removeBtn = document.getElementById(removeId);

            // Click to upload
            dropZone.addEventListener('click', () => input.click());

            // Drag and drop
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('border-blue-400', 'bg-blue-50');
            });

            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('border-blue-400', 'bg-blue-50');
            });

            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('border-blue-400', 'bg-blue-50');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    input.files = files;
                    handleFileSelect(files[0]);
                }
            });

            // File input change
            input.addEventListener('change', function() {
                if (this.files.length > 0) {
                    handleFileSelect(this.files[0]);
                }
            });

            // Remove file
            removeBtn.addEventListener('click', function() {
                input.value = '';
                resetFilePreview(inputId.replace('_file', ''));
            });

            function handleFileSelect(file) {
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                dropZone.classList.add('hidden');
                preview.classList.remove('hidden');
            }
        }

        function resetFilePreview(type) {
            const dropZone = document.getElementById(type === 'additional' ? 'additionalFileDropZone' : 'fileDropZone');
            const preview = document.getElementById(type === 'additional' ? 'additionalFilePreview' : 'filePreview');

            dropZone.classList.remove('hidden');
            preview.classList.add('hidden');
        }

        function resetForm() {
            document.getElementById('demoForm').reset();
            document.getElementById('resultsSection').classList.add('hidden');
            resetFilePreview('');
            resetFilePreview('additional');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>