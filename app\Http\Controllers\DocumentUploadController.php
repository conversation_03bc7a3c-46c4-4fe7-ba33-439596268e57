<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Services\DocumentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DocumentUploadController extends Controller
{
    protected $documentService;

    public function __construct(DocumentService $documentService)
    {
        $this->documentService = $documentService;
    }

    public function process(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
                'document_type' => 'required|string|exists:document_types,slug',
                'additional_file' => 'nullable|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
                'webhook_url' => 'nullable|url',
            ]);

            $user = $request->user();

            $document = $this->documentService->uploadDocument(
                $user,
                $request->file('file'),
                $request->document_type,
                $request->file('additional_file'),
                $request->webhook_url
            );

            // Start processing
            $this->documentService->processDocument($document);

            return redirect()->route('dashboard.documents.show', $document->uuid)
                           ->with('success', 'Document uploaded successfully and is being processed.');

        } catch (\Exception $e) {
            Log::error('Document upload failed', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
            ]);

            return back()->withInput()
                        ->with('error', $e->getMessage());
        }
    }

    public function show(Request $request, string $uuid)
    {
        $user = $request->user();
        $document = Document::where('uuid', $uuid)
                           ->where('user_id', $user->id)
                           ->with(['documentType', 'result'])
                           ->firstOrFail();

        return view('dashboard.document-show', compact('document'));
    }
}
