<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\User;
use App\Models\DocumentType;
use App\Models\Plan;
use App\Services\PlanService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    protected $planService;

    public function __construct(PlanService $planService)
    {
        $this->planService = $planService;
    }
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'total_documents' => Document::count(),
            'documents_today' => Document::whereDate('created_at', today())->count(),
            'active_users' => User::whereHas('documents', function($q) {
                $q->where('created_at', '>=', now()->subDays(30));
            })->count(),
        ];

        $recentDocuments = Document::with(['user', 'documentType'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $documentsByType = Document::join('document_types', 'documents.document_type_id', '=', 'document_types.id')
            ->select('document_types.name', DB::raw('count(*) as count'))
            ->groupBy('document_types.name')
            ->get();

        return view('admin.dashboard', compact('stats', 'recentDocuments', 'documentsByType'));
    }

    public function users(Request $request)
    {
        $query = User::with(['currentPlan.plan', 'documents']);

        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.users', compact('users'));
    }

    public function documents(Request $request)
    {
        $query = Document::with(['user', 'documentType', 'result']);

        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->document_type) {
            $query->whereHas('documentType', function($q) use ($request) {
                $q->where('slug', $request->document_type);
            });
        }

        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('original_filename', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%')
                               ->orWhere('email', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $documents = $query->orderBy('created_at', 'desc')->paginate(20);
        $documentTypes = DocumentType::all();

        return view('admin.documents', compact('documents', 'documentTypes'));
    }

    public function analytics()
    {
        // Daily usage for the last 30 days
        $dailyUsage = Document::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Success rate by document type
        $successRates = DocumentType::withCount([
            'documents',
            'documents as completed_count' => function($q) {
                $q->where('status', 'completed');
            }
        ])->get()->map(function($type) {
            return [
                'name' => $type->name,
                'success_rate' => $type->documents_count > 0
                    ? round(($type->completed_count / $type->documents_count) * 100, 2)
                    : 0,
                'total_documents' => $type->documents_count,
            ];
        });

        // Revenue by plan (if you have payment integration)
        $planUsage = Plan::withCount('userPlans')->get();

        return view('admin.analytics', compact('dailyUsage', 'successRates', 'planUsage'));
    }

    public function settings()
    {
        $documentTypes = DocumentType::orderBy('sort_order')->get();
        $plans = Plan::orderBy('sort_order')->get();

        return view('admin.settings', compact('documentTypes', 'plans'));
    }

    /**
     * Update user credits
     */
    public function updateCredits(Request $request, User $user)
    {
        $request->validate([
            'credits' => 'required|integer|min:0|max:999999',
        ]);

        $user->update([
            'remaining_credits' => $request->credits,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Credits updated successfully.',
            'new_credits' => $user->remaining_credits,
        ]);
    }

    /**
     * Suspend user account
     */
    public function suspendUser(User $user)
    {
        if ($user->email === '<EMAIL>') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot suspend admin user.',
            ], 403);
        }

        // Deactivate user's plan
        if ($user->currentPlan) {
            $user->currentPlan->update(['is_active' => false]);
        }

        // Revoke all API tokens
        $user->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'User suspended successfully.',
        ]);
    }

    /**
     * Activate user account
     */
    public function activateUser(User $user)
    {
        // Reactivate user's plan or assign free plan
        if ($user->currentPlan) {
            $user->currentPlan->update(['is_active' => true]);
        } else {
            $this->planService->assignFreePlanToUser($user);
        }

        return response()->json([
            'success' => true,
            'message' => 'User activated successfully.',
        ]);
    }

    /**
     * Update user plan
     */
    public function updatePlan(Request $request, User $user)
    {
        $request->validate([
            'plan_slug' => 'required|string|exists:plans,slug',
            'months' => 'required|integer|min:1|max:24',
        ]);

        try {
            $expiresAt = $request->plan_slug === 'free' ? null : now()->addMonths($request->months);
            $this->planService->upgradeUserPlan($user, $request->plan_slug, $expiresAt);

            return response()->json([
                'success' => true,
                'message' => 'Plan updated successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update plan: ' . $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Record offline payment
     */
    public function recordPayment(Request $request, User $user)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'plan_slug' => 'required|string|exists:plans,slug',
            'months' => 'required|integer|min:1|max:24',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $this->planService->recordOfflinePayment(
                $user,
                $request->amount,
                $request->plan_slug,
                $request->months,
                $request->notes
            );

            return response()->json([
                'success' => true,
                'message' => 'Payment recorded and plan updated successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to record payment: ' . $e->getMessage(),
            ], 400);
        }
    }
}
