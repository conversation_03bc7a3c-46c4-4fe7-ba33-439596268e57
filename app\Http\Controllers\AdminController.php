<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\User;
use App\Models\DocumentType;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'total_documents' => Document::count(),
            'documents_today' => Document::whereDate('created_at', today())->count(),
            'active_users' => User::whereHas('documents', function($q) {
                $q->where('created_at', '>=', now()->subDays(30));
            })->count(),
        ];

        $recentDocuments = Document::with(['user', 'documentType'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $documentsByType = Document::join('document_types', 'documents.document_type_id', '=', 'document_types.id')
            ->select('document_types.name', DB::raw('count(*) as count'))
            ->groupBy('document_types.name')
            ->get();

        return view('admin.dashboard', compact('stats', 'recentDocuments', 'documentsByType'));
    }

    public function users(Request $request)
    {
        $query = User::with(['currentPlan.plan', 'documents']);

        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.users', compact('users'));
    }

    public function documents(Request $request)
    {
        $query = Document::with(['user', 'documentType', 'result']);

        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->document_type) {
            $query->whereHas('documentType', function($q) use ($request) {
                $q->where('slug', $request->document_type);
            });
        }

        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('original_filename', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%')
                               ->orWhere('email', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $documents = $query->orderBy('created_at', 'desc')->paginate(20);
        $documentTypes = DocumentType::all();

        return view('admin.documents', compact('documents', 'documentTypes'));
    }

    public function analytics()
    {
        // Daily usage for the last 30 days
        $dailyUsage = Document::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Success rate by document type
        $successRates = DocumentType::withCount([
            'documents',
            'documents as completed_count' => function($q) {
                $q->where('status', 'completed');
            }
        ])->get()->map(function($type) {
            return [
                'name' => $type->name,
                'success_rate' => $type->documents_count > 0
                    ? round(($type->completed_count / $type->documents_count) * 100, 2)
                    : 0,
                'total_documents' => $type->documents_count,
            ];
        });

        // Revenue by plan (if you have payment integration)
        $planUsage = Plan::withCount('userPlans')->get();

        return view('admin.analytics', compact('dailyUsage', 'successRates', 'planUsage'));
    }

    public function settings()
    {
        $documentTypes = DocumentType::orderBy('sort_order')->get();
        $plans = Plan::orderBy('sort_order')->get();

        return view('admin.settings', compact('documentTypes', 'plans'));
    }
}
