<?php

namespace App\Services;

use App\Models\Document;
use App\Models\DocumentResult;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class OcrService
{
    public function processDocument(Document $document): bool
    {
        try {
            $document->update([
                'status' => 'processing',
                'processing_started_at' => now(),
            ]);

            // Simulate OCR processing for demo
            $extractedData = $this->simulateOcrProcessing($document);
            
            // Create document result
            DocumentResult::create([
                'document_id' => $document->id,
                'extracted_data' => $extractedData,
                'raw_ocr_response' => [
                    'confidence' => rand(85, 98) / 100,
                    'processing_time' => rand(1000, 5000),
                ],
                'processing_time_ms' => rand(1000, 5000),
            ]);

            $document->update([
                'status' => 'completed',
                'processing_completed_at' => now(),
                'confidence_score' => rand(85, 98) / 100,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('OCR processing failed', [
                'document_id' => $document->id,
                'error' => $e->getMessage(),
            ]);

            $document->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
            ]);

            return false;
        }
    }

    private function simulateOcrProcessing(Document $document): array
    {
        $documentType = $document->documentType;
        
        switch ($documentType->slug) {
            case 'nic':
                return [
                    'nic_number' => $this->generateFakeNic(),
                    'full_name' => 'JOHN DOE SILVA',
                    'date_of_birth' => '1990-01-15',
                    'address' => '123 Main Street, Colombo 01',
                    'gender' => 'Male',
                ];
                
            case 'driving_license':
                return [
                    'license_number' => 'B' . rand(1000000, 9999999),
                    'full_name' => 'JANE DOE PERERA',
                    'expiry_date' => '2026-12-31',
                    'vehicle_classes' => ['B1', 'A1'],
                ];
                
            case 'vehicle_registration':
                return [
                    'registration_number' => $this->generateVehicleNumber(),
                    'vehicle_make' => 'Toyota',
                    'vehicle_model' => 'Corolla',
                    'year' => '2020',
                    'engine_number' => 'ENG' . rand(100000, 999999),
                ];
                
            case 'land_deed':
                return [
                    'deed_number' => 'DEED' . rand(10000, 99999),
                    'property_address' => '456 Property Lane, Kandy',
                    'owner_name' => 'PROPERTY OWNER NAME',
                    'extent' => '2 Acres 1 Rood 20 Perches',
                ];
                
            case 'invoice':
                return [
                    'invoice_number' => 'INV-' . rand(1000, 9999),
                    'total_amount' => rand(1000, 50000),
                    'vendor_name' => 'ABC Company (Pvt) Ltd',
                    'invoice_date' => now()->format('Y-m-d'),
                    'due_date' => now()->addDays(30)->format('Y-m-d'),
                ];
                
            default:
                return [
                    'text_content' => 'Extracted text content from the document',
                    'confidence' => rand(80, 95) / 100,
                ];
        }
    }

    private function generateFakeNic(): string
    {
        $year = rand(1950, 2000);
        $dayOfYear = str_pad(rand(1, 365), 3, '0', STR_PAD_LEFT);
        $suffix = rand(0, 1) ? 'V' : 'X';
        
        return $year . $dayOfYear . rand(10000, 99999) . $suffix;
    }

    private function generateVehicleNumber(): string
    {
        $provinces = ['WP', 'CP', 'SP', 'NP', 'EP', 'NC', 'NW', 'UVA', 'SAB'];
        $province = $provinces[array_rand($provinces)];
        $letters = chr(rand(65, 90)) . chr(rand(65, 90));
        $numbers = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $province . '-' . $letters . '-' . $numbers;
    }

    public function uploadFile(UploadedFile $file, string $directory = 'documents'): string
    {
        $filename = time() . '_' . $file->getClientOriginalName();
        $path = $file->storeAs($directory, $filename, 'public');
        
        return $path;
    }

    public function deleteFile(string $path): bool
    {
        return Storage::disk('public')->delete($path);
    }

    public function getFileUrl(string $path): string
    {
        return Storage::disk('public')->url($path);
    }
}
