<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use <PERSON><PERSON>\Jetstream\HasTeams;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use HasProfilePhoto;
    use HasTeams;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the user's documents.
     */
    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get the user's current plan.
     */
    public function currentPlan()
    {
        return $this->hasOne(UserPlan::class)->current();
    }

    /**
     * Get all user plans.
     */
    public function userPlans()
    {
        return $this->hasMany(UserPlan::class);
    }

    /**
     * Get documents assigned for manual review.
     */
    public function assignedDocuments()
    {
        return $this->hasMany(Document::class, 'manual_review_assigned_to');
    }

    /**
     * Check if user has sufficient credits.
     */
    public function hasCredits(int $amount = 1): bool
    {
        $currentPlan = $this->currentPlan;
        return $currentPlan && $currentPlan->canUseCredits($amount);
    }

    /**
     * Use credits from user's current plan.
     */
    public function useCredits(int $amount = 1): bool
    {
        $currentPlan = $this->currentPlan;
        return $currentPlan && $currentPlan->useCredits($amount);
    }

    /**
     * Get remaining credits.
     */
    public function getRemainingCreditsAttribute(): int
    {
        $currentPlan = $this->currentPlan;
        return $currentPlan ? $currentPlan->credits_remaining : 0;
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->email === '<EMAIL>' ||
               $this->role === 'admin' ||
               str_ends_with($this->email, '@smartocr.lk');
    }
}
