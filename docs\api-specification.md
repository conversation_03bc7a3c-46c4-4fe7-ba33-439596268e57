# API Specification

## Overview
RESTful API for SmartOCR.lk platform with Laravel Sanctum authentication.

**Base URL**: `https://api.smartocr.lk/api/v1`

## Authentication

### Get API Token
```http
POST /auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "token": "1|abc123...",
        "user": {
            "id": 1,
            "name": "<PERSON>",
            "email": "<EMAIL>"
        },
        "expires_at": "2024-01-01T00:00:00Z"
    }
}
```

### Using Token
```http
Authorization: Bearer 1|abc123...
```

## Document Processing

### Upload Document
```http
POST /documents/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}

Fields:
- file: (required) Document file (max 10MB)
- document_type: (required) One of: nic, driving_license, vehicle_registration, land_deed, invoice
- additional_file: (optional) Second image for NIC/driving license
- webhook_url: (optional) URL to receive results
```

**Response (Success):**
```json
{
    "success": true,
    "data": {
        "document_id": "550e8400-e29b-41d4-a716-************",
        "status": "pending",
        "credits_used": 1,
        "estimated_processing_time": 30,
        "expires_at": "2024-01-02T00:00:00Z"
    }
}
```

**Response (Insufficient Credits):**
```json
{
    "success": false,
    "error": {
        "code": "INSUFFICIENT_CREDITS",
        "message": "Not enough credits. Required: 1, Available: 0",
        "details": {
            "required_credits": 1,
            "available_credits": 0
        }
    }
}
```

### Get Document Result
```http
GET /documents/{uuid}
Authorization: Bearer {token}
```

**Response (Completed):**
```json
{
    "success": true,
    "data": {
        "document_id": "550e8400-e29b-41d4-a716-************",
        "status": "completed",
        "document_type": "nic",
        "confidence_score": 0.95,
        "processing_time_ms": 2340,
        "result": {
            "nic_number": "123456789V",
            "full_name": "JOHN DOE",
            "date_of_birth": "1990-01-01",
            "address": "123 Main St, Colombo 01",
            "gender": "Male"
        },
        "created_at": "2024-01-01T10:00:00Z",
        "completed_at": "2024-01-01T10:00:30Z"
    }
}
```

**Response (Processing):**
```json
{
    "success": true,
    "data": {
        "document_id": "550e8400-e29b-41d4-a716-************",
        "status": "processing",
        "estimated_completion": "2024-01-01T10:01:00Z"
    }
}
```

**Response (Failed):**
```json
{
    "success": false,
    "data": {
        "document_id": "550e8400-e29b-41d4-a716-************",
        "status": "failed",
        "error_message": "Unable to detect text in the provided image"
    }
}
```

### List Documents
```http
GET /documents?page=1&per_page=20&status=completed&document_type=nic
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "documents": [
            {
                "document_id": "550e8400-e29b-41d4-a716-************",
                "document_type": "nic",
                "status": "completed",
                "confidence_score": 0.95,
                "created_at": "2024-01-01T10:00:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 45,
            "last_page": 3
        }
    }
}
```

## Usage & Credits

### Get Usage Statistics
```http
GET /usage
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "current_plan": {
            "name": "Pro",
            "monthly_credits": 500,
            "credits_remaining": 245,
            "credits_used_this_month": 255,
            "billing_cycle_end": "2024-01-31"
        },
        "usage_this_month": {
            "total_documents": 255,
            "by_type": {
                "nic": 120,
                "driving_license": 85,
                "invoice": 50
            },
            "success_rate": 0.96
        }
    }
}
```

### Get Usage History
```http
GET /usage/history?from=2024-01-01&to=2024-01-31&group_by=day
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "period": {
            "from": "2024-01-01",
            "to": "2024-01-31",
            "group_by": "day"
        },
        "usage": [
            {
                "date": "2024-01-01",
                "documents_processed": 12,
                "credits_used": 15,
                "success_rate": 0.92
            }
        ]
    }
}
```

## Webhook Management

### Register Webhook
```http
POST /webhooks/register
Content-Type: application/json
Authorization: Bearer {token}

{
    "url": "https://yourapp.com/webhook/ocr-results",
    "events": ["document.completed", "document.failed"],
    "secret": "your-webhook-secret"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "webhook_id": 123,
        "url": "https://yourapp.com/webhook/ocr-results",
        "events": ["document.completed", "document.failed"],
        "created_at": "2024-01-01T10:00:00Z"
    }
}
```

### Webhook Payload
When a document is processed, we'll send:

```json
{
    "event": "document.completed",
    "document_id": "550e8400-e29b-41d4-a716-************",
    "status": "completed",
    "result": {
        "nic_number": "123456789V",
        "full_name": "JOHN DOE"
    },
    "timestamp": "2024-01-01T10:00:30Z",
    "signature": "sha256=abc123..."
}
```

## Document Types

### Get Available Document Types
```http
GET /document-types
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "slug": "nic",
            "name": "National Identity Card",
            "description": "Sri Lankan National Identity Card",
            "credit_cost": 1,
            "max_file_size_mb": 10,
            "supports_additional_file": true,
            "allowed_extensions": ["jpg", "jpeg", "png", "pdf"]
        }
    ]
}
```

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_TOKEN` | 401 | Invalid or expired authentication token |
| `INSUFFICIENT_CREDITS` | 402 | Not enough credits to process document |
| `INVALID_FILE_TYPE` | 400 | Unsupported file format |
| `FILE_TOO_LARGE` | 413 | File exceeds maximum size limit |
| `DOCUMENT_NOT_FOUND` | 404 | Document ID not found |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `PROCESSING_FAILED` | 422 | OCR processing failed |
| `WEBHOOK_INVALID` | 400 | Invalid webhook URL or configuration |

## Rate Limiting

- **Free Plan**: 10 requests/minute
- **Pro Plan**: 60 requests/minute  
- **Enterprise Plan**: 200 requests/minute

Rate limit headers:
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

## Response Format

All responses follow this structure:

**Success:**
```json
{
    "success": true,
    "data": { ... }
}
```

**Error:**
```json
{
    "success": false,
    "error": {
        "code": "ERROR_CODE",
        "message": "Human readable message",
        "details": { ... }
    }
}
```

## SDK Examples

### PHP (Laravel)
```php
use Illuminate\Support\Facades\Http;

$response = Http::withToken($token)
    ->attach('file', file_get_contents($filePath), 'document.jpg')
    ->post('https://api.smartocr.lk/api/v1/documents/upload', [
        'document_type' => 'nic'
    ]);
```

### JavaScript
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('document_type', 'nic');

fetch('https://api.smartocr.lk/api/v1/documents/upload', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`
    },
    body: formData
});
```

### Python
```python
import requests

files = {'file': open('document.jpg', 'rb')}
data = {'document_type': 'nic'}
headers = {'Authorization': f'Bearer {token}'}

response = requests.post(
    'https://api.smartocr.lk/api/v1/documents/upload',
    files=files,
    data=data,
    headers=headers
)
```