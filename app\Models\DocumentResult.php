<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DocumentResult extends Model
{
    protected $fillable = [
        'document_id',
        'extracted_data',
        'raw_ocr_response',
        'processing_time_ms',
        'validation_errors',
        'manual_corrections',
    ];

    protected $casts = [
        'extracted_data' => 'array',
        'raw_ocr_response' => 'array',
        'validation_errors' => 'array',
        'manual_corrections' => 'array',
    ];

    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    public function getProcessingTimeAttribute(): string
    {
        if (!$this->processing_time_ms) {
            return 'N/A';
        }

        if ($this->processing_time_ms < 1000) {
            return $this->processing_time_ms . 'ms';
        }

        return round($this->processing_time_ms / 1000, 2) . 's';
    }

    public function hasValidationErrors(): bool
    {
        return !empty($this->validation_errors);
    }

    public function hasManualCorrections(): bool
    {
        return !empty($this->manual_corrections);
    }
}
