@extends('layouts.dashboard')

@section('title', 'API Tokens')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">API Tokens</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage your API tokens for programmatic access</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ url('/api/documentation') }}" target="_blank" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        <i class="fas fa-book mr-2"></i>
                        API Documentation
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Token -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Create New Token</h3>
            
            <form action="{{ route('dashboard.api-tokens.store') }}" method="POST" class="space-y-4">
                @csrf
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Token Name
                    </label>
                    <input type="text" id="name" name="name" required
                           placeholder="e.g., My Application API Token"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        <i class="fas fa-plus mr-2"></i>
                        Create Token
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- New Token Display -->
    @if(session('token'))
    <div class="bg-green-50 border border-green-200 rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400 text-xl"></i>
                </div>
                <div class="ml-3 flex-1">
                    <h3 class="text-sm font-medium text-green-800">Token Created Successfully</h3>
                    <div class="mt-2">
                        <p class="text-sm text-green-700 mb-2">
                            Please copy your new API token. For security reasons, it won't be shown again.
                        </p>
                        <div class="bg-white border border-green-300 rounded-md p-3">
                            <div class="flex items-center justify-between">
                                <code class="text-sm font-mono text-gray-900 break-all">{{ session('token') }}</code>
                                <button onclick="copyToken('{{ session('token') }}')" class="ml-2 text-green-600 hover:text-green-500">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Existing Tokens -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Your API Tokens</h3>
            
            @if($tokens->count() > 0)
                <div class="space-y-3">
                    @foreach($tokens as $token)
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-key text-gray-400"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $token->name }}</p>
                                <p class="text-xs text-gray-500">
                                    Created {{ $token->created_at->diffForHumans() }}
                                    @if($token->last_used_at)
                                        • Last used {{ $token->last_used_at->diffForHumans() }}
                                    @else
                                        • Never used
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <form action="{{ route('dashboard.api-tokens.destroy', $token->id) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        onclick="return confirm('Are you sure you want to delete this token? This action cannot be undone.')"
                                        class="text-red-600 hover:text-red-500 text-sm">
                                    <i class="fas fa-trash mr-1"></i>
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-6">
                    <i class="fas fa-key text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500">No API tokens created yet</p>
                    <p class="text-sm text-gray-400 mt-1">Create your first token to start using the API</p>
                </div>
            @endif
        </div>
    </div>

    <!-- API Usage Examples -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">API Usage Examples</h3>
            
            <div class="space-y-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Authentication</h4>
                    <div class="bg-gray-100 rounded-md p-3">
                        <code class="text-sm text-gray-800">
                            Authorization: Bearer YOUR_API_TOKEN
                        </code>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Upload Document (cURL)</h4>
                    <div class="bg-gray-100 rounded-md p-3 overflow-x-auto">
                        <pre class="text-sm text-gray-800">curl -X POST {{ config('app.url') }}/api/v1/documents/upload \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -F "file=@document.jpg" \
  -F "document_type=nic"</pre>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Get Document Result (cURL)</h4>
                    <div class="bg-gray-100 rounded-md p-3 overflow-x-auto">
                        <pre class="text-sm text-gray-800">curl -X GET {{ config('app.url') }}/api/v1/documents/DOCUMENT_UUID \
  -H "Authorization: Bearer YOUR_API_TOKEN"</pre>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Need more help?</h4>
                        <p class="text-sm text-gray-600">Check out our comprehensive API documentation</p>
                    </div>
                    <a href="{{ url('/api/documentation') }}" target="_blank" class="text-blue-600 hover:text-blue-500 text-sm">
                        View Full Documentation →
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function copyToken(token) {
    navigator.clipboard.writeText(token).then(() => {
        // Show success message
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('text-green-600');
        
        setTimeout(() => {
            button.innerHTML = originalIcon;
            button.classList.remove('text-green-600');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy token: ', err);
        alert('Failed to copy token to clipboard');
    });
}
</script>
@endpush
@endsection
