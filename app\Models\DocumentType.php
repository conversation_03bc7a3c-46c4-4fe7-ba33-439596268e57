<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DocumentType extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'credit_cost',
        'max_file_size_mb',
        'allowed_extensions',
        'supports_additional_file',
        'processing_time_estimate_seconds',
        'is_active',
        'validation_rules',
    ];

    protected $casts = [
        'allowed_extensions' => 'array',
        'supports_additional_file' => 'boolean',
        'is_active' => 'boolean',
        'validation_rules' => 'array',
    ];

    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function getFormattedPriceAttribute(): string
    {
        return $this->credit_cost . ' credit' . ($this->credit_cost > 1 ? 's' : '');
    }
}
