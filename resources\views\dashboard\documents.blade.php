@extends('layouts.dashboard')

@section('title', 'Documents')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Documents</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage and view your processed documents</p>
                </div>
                <div class="flex space-x-3">
                    <button onclick="showExportModal()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition">
                        <i class="fas fa-download mr-2"></i>
                        Export Data
                    </button>
                    <a href="{{ route('dashboard.upload') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        <i class="fas fa-upload mr-2"></i>
                        Upload Document
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" action="{{ route('dashboard.documents') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="processing" {{ request('status') === 'processing' ? 'selected' : '' }}>Processing</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                        <option value="manual_review" {{ request('status') === 'manual_review' ? 'selected' : '' }}>Manual Review</option>
                    </select>
                </div>

                <!-- Document Type Filter -->
                <div>
                    <label for="document_type" class="block text-sm font-medium text-gray-700 mb-1">Document Type</label>
                    <select name="document_type" id="document_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Types</option>
                        @foreach($documentTypes as $type)
                            <option value="{{ $type->slug }}" {{ request('document_type') === $type->slug ? 'selected' : '' }}>
                                {{ $type->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Date Range -->
                <div>
                    <label for="from_date" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                    <input type="date" name="from_date" id="from_date" value="{{ request('from_date') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- Search and Filter Button -->
                <div class="flex items-end space-x-2">
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                    @if(request()->hasAny(['status', 'document_type', 'from_date']))
                        <a href="{{ route('dashboard.documents') }}" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition">
                            Clear
                        </a>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- Documents List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            @if($documents->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Document
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Type
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Credits
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Uploaded
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($documents as $document)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center">
                                                @if(str_contains($document->mime_type, 'pdf'))
                                                    <i class="fas fa-file-pdf text-red-500"></i>
                                                @else
                                                    <i class="fas fa-file-image text-blue-500"></i>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ Str::limit($document->original_filename, 30) }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ $document->formatted_file_size }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $document->documentType->name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($document->status === 'completed') bg-green-100 text-green-800
                                        @elseif($document->status === 'failed') bg-red-100 text-red-800
                                        @elseif($document->status === 'processing') bg-blue-100 text-blue-800
                                        @elseif($document->status === 'manual_review') bg-yellow-100 text-yellow-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        @if($document->status === 'processing')
                                            <i class="fas fa-spinner fa-spin mr-1"></i>
                                        @elseif($document->status === 'completed')
                                            <i class="fas fa-check mr-1"></i>
                                        @elseif($document->status === 'failed')
                                            <i class="fas fa-times mr-1"></i>
                                        @elseif($document->status === 'manual_review')
                                            <i class="fas fa-eye mr-1"></i>
                                        @else
                                            <i class="fas fa-clock mr-1"></i>
                                        @endif
                                        {{ ucfirst(str_replace('_', ' ', $document->status)) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $document->credits_used }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div>{{ $document->created_at->format('M j, Y') }}</div>
                                    <div>{{ $document->created_at->format('g:i A') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="{{ route('dashboard.documents.show', $document->uuid) }}" 
                                           class="text-blue-600 hover:text-blue-900">
                                            View
                                        </a>
                                        @if($document->status === 'completed' && $document->result)
                                            <button onclick="downloadResults('{{ $document->uuid }}')" 
                                                    class="text-green-600 hover:text-green-900">
                                                Download
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $documents->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-file-alt text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
                    <p class="text-gray-600 mb-6">
                        @if(request()->hasAny(['status', 'document_type', 'from_date']))
                            No documents match your current filters.
                        @else
                            You haven't uploaded any documents yet.
                        @endif
                    </p>
                    <div class="space-x-3">
                        <a href="{{ route('dashboard.upload') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                            Upload Your First Document
                        </a>
                        @if(request()->hasAny(['status', 'document_type', 'from_date']))
                            <a href="{{ route('dashboard.documents') }}" class="text-gray-600 hover:text-gray-900">
                                Clear Filters
                            </a>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Bulk Actions (if needed in future) -->
    @if($documents->count() > 0)
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    Showing {{ $documents->firstItem() ?? 0 }} to {{ $documents->lastItem() ?? 0 }} of {{ $documents->total() }} documents
                </div>
                <div class="flex space-x-2">
                    <button onclick="exportDocuments()" class="text-blue-600 hover:text-blue-900 text-sm">
                        <i class="fas fa-download mr-1"></i>
                        Export List
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
function downloadResults(documentUuid) {
    // This would typically make an API call to get the results
    window.location.href = `/api/v1/documents/${documentUuid}/download`;
}

function showExportModal() {
    document.getElementById('exportModal').classList.remove('hidden');
}

function hideExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

// Auto-refresh for documents with processing status
@if($documents->where('status', 'processing')->count() > 0 || $documents->where('status', 'pending')->count() > 0)
setTimeout(() => {
    window.location.reload();
}, 30000); // Refresh every 30 seconds if there are processing documents
@endif

// Close modal when clicking outside
document.getElementById('exportModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        hideExportModal();
    }
});
</script>
@endpush

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Export Documents</h3>
                <button onclick="hideExportModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form action="{{ route('dashboard.export') }}" method="POST" class="space-y-4">
                @csrf

                <div>
                    <label for="export_document_type" class="block text-sm font-medium text-gray-700 mb-1">Document Type</label>
                    <select name="document_type" id="export_document_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Types</option>
                        @foreach($documentTypes as $type)
                            <option value="{{ $type->slug }}">{{ $type->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                        <input type="date" name="date_from" id="date_from" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                        <input type="date" name="date_to" id="date_to" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div>
                    <label for="export_format" class="block text-sm font-medium text-gray-700 mb-1">Format</label>
                    <select name="format" id="export_format" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="csv">CSV</option>
                        <option value="excel">Excel (Coming Soon)</option>
                    </select>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideExportModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700">
                        <i class="fas fa-download mr-2"></i>
                        Export
                    </button>
                </div>
            </form>

            <div class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-sm text-gray-600 mb-2">Download sample templates:</p>
                <div class="flex flex-wrap gap-2">
                    @foreach($documentTypes as $type)
                        <a href="{{ route('dashboard.export.template', $type->slug) }}"
                           class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200">
                            {{ $type->name }} Template
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
