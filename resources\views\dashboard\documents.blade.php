@extends('layouts.dashboard')

@section('title', 'Documents')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Documents</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage and view your processed documents</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('dashboard.upload') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        <i class="fas fa-upload mr-2"></i>
                        Upload Document
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" action="{{ route('dashboard.documents') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="processing" {{ request('status') === 'processing' ? 'selected' : '' }}>Processing</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                        <option value="manual_review" {{ request('status') === 'manual_review' ? 'selected' : '' }}>Manual Review</option>
                    </select>
                </div>

                <!-- Document Type Filter -->
                <div>
                    <label for="document_type" class="block text-sm font-medium text-gray-700 mb-1">Document Type</label>
                    <select name="document_type" id="document_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Types</option>
                        @foreach($documentTypes as $type)
                            <option value="{{ $type->slug }}" {{ request('document_type') === $type->slug ? 'selected' : '' }}>
                                {{ $type->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Date Range -->
                <div>
                    <label for="from_date" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                    <input type="date" name="from_date" id="from_date" value="{{ request('from_date') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- Search and Filter Button -->
                <div class="flex items-end space-x-2">
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                    @if(request()->hasAny(['status', 'document_type', 'from_date']))
                        <a href="{{ route('dashboard.documents') }}" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition">
                            Clear
                        </a>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- Documents List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            @if($documents->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Document
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Type
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Credits
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Uploaded
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($documents as $document)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center">
                                                @if(str_contains($document->mime_type, 'pdf'))
                                                    <i class="fas fa-file-pdf text-red-500"></i>
                                                @else
                                                    <i class="fas fa-file-image text-blue-500"></i>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ Str::limit($document->original_filename, 30) }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ $document->formatted_file_size }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $document->documentType->name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($document->status === 'completed') bg-green-100 text-green-800
                                        @elseif($document->status === 'failed') bg-red-100 text-red-800
                                        @elseif($document->status === 'processing') bg-blue-100 text-blue-800
                                        @elseif($document->status === 'manual_review') bg-yellow-100 text-yellow-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        @if($document->status === 'processing')
                                            <i class="fas fa-spinner fa-spin mr-1"></i>
                                        @elseif($document->status === 'completed')
                                            <i class="fas fa-check mr-1"></i>
                                        @elseif($document->status === 'failed')
                                            <i class="fas fa-times mr-1"></i>
                                        @elseif($document->status === 'manual_review')
                                            <i class="fas fa-eye mr-1"></i>
                                        @else
                                            <i class="fas fa-clock mr-1"></i>
                                        @endif
                                        {{ ucfirst(str_replace('_', ' ', $document->status)) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $document->credits_used }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div>{{ $document->created_at->format('M j, Y') }}</div>
                                    <div>{{ $document->created_at->format('g:i A') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="{{ route('dashboard.documents.show', $document->uuid) }}" 
                                           class="text-blue-600 hover:text-blue-900">
                                            View
                                        </a>
                                        @if($document->status === 'completed' && $document->result)
                                            <button onclick="downloadResults('{{ $document->uuid }}')" 
                                                    class="text-green-600 hover:text-green-900">
                                                Download
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $documents->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-file-alt text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
                    <p class="text-gray-600 mb-6">
                        @if(request()->hasAny(['status', 'document_type', 'from_date']))
                            No documents match your current filters.
                        @else
                            You haven't uploaded any documents yet.
                        @endif
                    </p>
                    <div class="space-x-3">
                        <a href="{{ route('dashboard.upload') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                            Upload Your First Document
                        </a>
                        @if(request()->hasAny(['status', 'document_type', 'from_date']))
                            <a href="{{ route('dashboard.documents') }}" class="text-gray-600 hover:text-gray-900">
                                Clear Filters
                            </a>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Bulk Actions (if needed in future) -->
    @if($documents->count() > 0)
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    Showing {{ $documents->firstItem() ?? 0 }} to {{ $documents->lastItem() ?? 0 }} of {{ $documents->total() }} documents
                </div>
                <div class="flex space-x-2">
                    <button onclick="exportDocuments()" class="text-blue-600 hover:text-blue-900 text-sm">
                        <i class="fas fa-download mr-1"></i>
                        Export List
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
function downloadResults(documentUuid) {
    // This would typically make an API call to get the results
    window.location.href = `/api/v1/documents/${documentUuid}/download`;
}

function exportDocuments() {
    // Export current filtered list as CSV
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = `{{ route('dashboard.documents') }}?${params.toString()}`;
}

// Auto-refresh for documents with processing status
@if($documents->where('status', 'processing')->count() > 0 || $documents->where('status', 'pending')->count() > 0)
setTimeout(() => {
    window.location.reload();
}, 30000); // Refresh every 30 seconds if there are processing documents
@endif
</script>
@endpush
@endsection
