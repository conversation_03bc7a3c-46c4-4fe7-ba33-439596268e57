<!-- Credits Management Modal -->
<div id="creditsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Manage Credits</h3>
                <button onclick="hideCreditsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="creditsForm" class="space-y-4">
                @csrf
                <input type="hidden" id="credits_user_id" name="user_id">
                
                <div>
                    <label for="credits_action" class="block text-sm font-medium text-gray-700 mb-1">Action</label>
                    <select id="credits_action" name="action" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="set">Set Credits To</option>
                        <option value="add">Add Credits</option>
                        <option value="subtract">Subtract Credits</option>
                    </select>
                </div>
                
                <div>
                    <label for="credits_amount" class="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                    <input type="number" id="credits_amount" name="credits" required min="0" max="999999"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="credits_reason" class="block text-sm font-medium text-gray-700 mb-1">Reason (Optional)</label>
                    <textarea id="credits_reason" name="reason" rows="3" 
                              placeholder="e.g., Bonus credits for loyal customer"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                This action will be logged and cannot be undone. Make sure the amount and reason are correct.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideCreditsModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        <i class="fas fa-save mr-2"></i>
                        Update Credits
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
