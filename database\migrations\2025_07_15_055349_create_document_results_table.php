<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('document_id')->unique()->constrained()->onDelete('cascade');
            $table->json('extracted_data');
            $table->json('raw_ocr_response')->nullable();
            $table->integer('processing_time_ms')->nullable();
            $table->json('validation_errors')->nullable();
            $table->json('manual_corrections')->nullable();
            $table->timestamps();

            $table->index('document_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_results');
    }
};
