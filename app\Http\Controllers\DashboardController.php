<?php

namespace App\Http\Controllers;

use App\Models\DocumentType;
use App\Services\DocumentService;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    protected $documentService;

    public function __construct(DocumentService $documentService)
    {
        $this->documentService = $documentService;
    }

    public function index(Request $request)
    {
        $user = $request->user();
        $currentPlan = $user->currentPlan;

        // Get recent documents
        $recentDocuments = $user->documents()
            ->with(['documentType', 'result'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get usage statistics
        $totalDocuments = $user->documents()->count();
        $completedDocuments = $user->documents()->where('status', 'completed')->count();
        $pendingDocuments = $user->documents()->whereIn('status', ['pending', 'processing'])->count();

        return view('dashboard.index', compact(
            'user',
            'currentPlan',
            'recentDocuments',
            'totalDocuments',
            'completedDocuments',
            'pendingDocuments'
        ));
    }

    public function documents(Request $request)
    {
        $user = $request->user();
        $documents = $this->documentService->getUserDocuments($user, $request->all());
        $documentTypes = DocumentType::active()->get();

        return view('dashboard.documents', compact('documents', 'documentTypes'));
    }

    public function upload()
    {
        $documentTypes = DocumentType::active()->get();

        return view('dashboard.upload', compact('documentTypes'));
    }

    public function usage(Request $request)
    {
        $user = $request->user();
        $currentPlan = $user->currentPlan;

        // Get usage data for charts
        $usageData = $user->documents()
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('dashboard.usage', compact('user', 'currentPlan', 'usageData'));
    }


}
