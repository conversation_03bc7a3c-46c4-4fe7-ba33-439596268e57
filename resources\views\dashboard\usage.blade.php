@extends('layouts.dashboard')

@section('title', 'Usage Analytics')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Usage Analytics</h1>
                    <p class="mt-1 text-sm text-gray-600">Track your document processing usage and performance</p>
                </div>
                <div class="text-right">
                    @if($currentPlan)
                        <p class="text-sm text-gray-600">Current Plan</p>
                        <p class="text-lg font-bold text-blue-600">{{ $currentPlan->plan->name }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @if($currentPlan)
    <!-- Plan Usage Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Credits Used -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-coins text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Credits Used</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ number_format($currentPlan->credits_used_this_month) }} / {{ number_format($currentPlan->plan->monthly_credits) }}
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ min(100, $currentPlan->usage_percentage) }}%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">{{ number_format($currentPlan->usage_percentage, 1) }}% used</p>
                </div>
            </div>
        </div>

        <!-- Credits Remaining -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-battery-three-quarters text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Credits Remaining</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($currentPlan->credits_remaining) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Billing Cycle -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-calendar text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Cycle Ends</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $currentPlan->billing_cycle_end->format('M j') }}</dd>
                        </dl>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-1">{{ $currentPlan->billing_cycle_end->diffForHumans() }}</p>
            </div>
        </div>

        <!-- Success Rate -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Success Rate</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                @php
                                    $totalDocs = $user->documents()->count();
                                    $successfulDocs = $user->documents()->where('status', 'completed')->count();
                                    $successRate = $totalDocs > 0 ? ($successfulDocs / $totalDocs) * 100 : 0;
                                @endphp
                                {{ number_format($successRate, 1) }}%
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage Chart -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Usage Over Time</h3>
            <div class="h-64">
                <canvas id="usageChart"></canvas>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Document Types Breakdown -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Documents by Type</h3>
                <div class="space-y-3">
                    @php
                        $documentsByType = $user->documents()
                            ->join('document_types', 'documents.document_type_id', '=', 'document_types.id')
                            ->selectRaw('document_types.name, document_types.slug, COUNT(*) as count')
                            ->groupBy('document_types.id', 'document_types.name', 'document_types.slug')
                            ->get();
                        $totalDocuments = $documentsByType->sum('count');
                    @endphp
                    
                    @foreach($documentsByType as $typeData)
                        @php
                            $percentage = $totalDocuments > 0 ? ($typeData->count / $totalDocuments) * 100 : 0;
                        @endphp
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-blue-500 rounded"></div>
                                <span class="text-sm text-gray-700">{{ $typeData->name }}</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-900">{{ $typeData->count }}</span>
                                <span class="text-xs text-gray-500">({{ number_format($percentage, 1) }}%)</span>
                            </div>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                        </div>
                    @endforeach
                    
                    @if($documentsByType->isEmpty())
                        <div class="text-center py-6">
                            <i class="fas fa-chart-pie text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500">No documents processed yet</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
                <div class="space-y-3">
                    @php
                        $recentDocuments = $user->documents()
                            ->with('documentType')
                            ->orderBy('created_at', 'desc')
                            ->limit(5)
                            ->get();
                    @endphp
                    
                    @foreach($recentDocuments as $document)
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                @if($document->status === 'completed')
                                    <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-green-600 text-xs"></i>
                                    </div>
                                @elseif($document->status === 'failed')
                                    <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-times text-red-600 text-xs"></i>
                                    </div>
                                @elseif($document->status === 'processing')
                                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-spinner fa-spin text-blue-600 text-xs"></i>
                                    </div>
                                @else
                                    <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-clock text-yellow-600 text-xs"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-900 truncate">{{ $document->original_filename }}</p>
                                <p class="text-xs text-gray-500">{{ $document->documentType->name }} • {{ $document->created_at->diffForHumans() }}</p>
                            </div>
                            <div class="text-xs text-gray-500">
                                {{ $document->credits_used }} credit{{ $document->credits_used > 1 ? 's' : '' }}
                            </div>
                        </div>
                    @endforeach
                    
                    @if($recentDocuments->isEmpty())
                        <div class="text-center py-6">
                            <i class="fas fa-history text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500">No recent activity</p>
                        </div>
                    @endif
                </div>
                
                @if($recentDocuments->count() > 0)
                    <div class="mt-4 text-center">
                        <a href="{{ route('dashboard.documents') }}" class="text-blue-600 hover:text-blue-500 text-sm">
                            View all documents →
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Plan Comparison (if on free plan) -->
    @if($currentPlan->plan->isFree())
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Need More Credits?</h3>
                    <p class="text-sm text-gray-600 mt-1">Upgrade to Pro for 1,000 monthly credits and advanced features</p>
                </div>
                <div class="flex space-x-3">
                    <a href="#" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        Upgrade Plan
                    </a>
                </div>
            </div>
        </div>
    </div>
    @endif
    @else
    <!-- No Plan Warning -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-yellow-800">No Active Plan</h3>
                    <p class="text-sm text-yellow-700 mt-1">You need an active plan to process documents and view usage analytics.</p>
                    <div class="mt-4">
                        <a href="#" class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 transition">
                            Choose a Plan
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($currentPlan && $usageData->count() > 0)
    // Usage Chart
    const ctx = document.getElementById('usageChart').getContext('2d');
    const usageData = @json($usageData);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: usageData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }),
            datasets: [{
                label: 'Documents Processed',
                data: usageData.map(item => item.count),
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
    @else
    // Show empty chart message
    const chartContainer = document.getElementById('usageChart').parentElement;
    chartContainer.innerHTML = `
        <div class="flex items-center justify-center h-64">
            <div class="text-center">
                <i class="fas fa-chart-line text-4xl text-gray-300 mb-4"></i>
                <p class="text-gray-500">No usage data available</p>
            </div>
        </div>
    `;
    @endif
});
</script>
@endpush
@endsection
