<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed basic data
        $this->call([
            DocumentTypesSeeder::class,
            PlansSeeder::class,
        ]);

        // Create admin user
        User::factory()->withPersonalTeam()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Create test user
        User::factory()->withPersonalTeam()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Assign plans to users
        $this->call([
            UserPlanSeeder::class,
        ]);
    }
}
