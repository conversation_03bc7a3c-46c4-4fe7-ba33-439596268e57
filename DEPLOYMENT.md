# SmartOCR Deployment Guide

This guide covers deploying SmartOCR updates to your server, including the latest changes for automatic Free plan assignment and enhanced features.

## 🚀 Quick Deployment (Existing Server)

If you already have SmartOCR running on your server and want to update to the latest version:

### 1. Pull Latest Changes
```bash
cd /path/to/your/smartocr
git pull origin master
```

### 2. Install/Update Dependencies
```bash
composer install --optimize-autoloader --no-dev
npm install && npm run build
```

### 3. Run New Migrations
```bash
php artisan migrate
```

### 4. Clear Caches
```bash
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear
```

### 5. Assign Free Plans to Existing Users (One-time)
```bash
php artisan db:seed --class=UserPlanSeeder
```

### 6. Set Permissions (if needed)
```bash
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

## 📋 What's New in This Update

### New Features Added:
- ✅ **Automatic Free Plan Assignment**: New users get 10 credits automatically
- ✅ **Real-time Document Processing**: No more pending states
- ✅ **Field-based Data Export**: CSV export with actual OCR data
- ✅ **Enhanced Admin Panel**: Company management with payment tracking
- ✅ **API Documentation**: Swagger UI integration
- ✅ **Document Field Configuration**: Configurable fields per document type

### Database Changes:
- Added `remaining_credits` column to `users` table
- Updated `user_plans` table structure (removed billing cycle fields, added starts_at/expires_at)
- New document field configuration system

## 🗄️ Fresh Installation

If you're setting up SmartOCR for the first time:

### 1. Clone Repository
```bash
git clone <your-repo-url> smartocr
cd smartocr
```

### 2. Install Dependencies
```bash
composer install
npm install && npm run build
```

### 3. Environment Setup
```bash
cp .env.example .env
php artisan key:generate
```

### 4. Configure Database
Edit `.env` file:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=smartocr
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 5. Run Migrations and Seeders
```bash
php artisan migrate:fresh --seed
```

### 6. Set Permissions
```bash
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

## 🔧 Configuration Updates

### New Configuration Files:
- `config/document-fields.php` - Document field definitions
- Enhanced `config/jetstream.php` - API features enabled

### Environment Variables:
Make sure these are set in your `.env`:
```env
# OCR Service (use your domain)
OCR_SERVICE_URL=http://yourdomain.com/api/v1/documents/process

# Session Driver (recommended)
SESSION_DRIVER=file

# App URL (update to your domain)
APP_URL=http://yourdomain.com
```

## 🔍 Verification Steps

After deployment, verify everything is working:

### 1. Check Database
```bash
php artisan tinker
```
```php
// Check if plans exist
App\Models\Plan::all();

// Check if users have plans
App\Models\User::with('currentPlan')->get();
```

### 2. Test New User Registration
- Register a new user
- Verify they automatically get Free plan with 10 credits
- Check dashboard shows plan information

### 3. Test Admin Features
- Login as admin (`<EMAIL>`)
- Access `/admin/dashboard`
- Verify company management features work

### 4. Test API Documentation
- Visit `/api/documentation`
- Verify Swagger UI loads correctly

### 5. Test Export Functionality
- Upload and process a document
- Try exporting data via the new export modal
- Verify CSV contains actual OCR field data

## 🚨 Troubleshooting

### Common Issues:

#### "No Active Plan" Error
```bash
# Run the user plan seeder to assign free plans
php artisan db:seed --class=UserPlanSeeder
```

#### Migration Errors
```bash
# If you get column already exists errors
php artisan migrate:status
# Check which migrations are pending and run them individually
```

#### Permission Issues
```bash
# Fix storage permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 755 storage bootstrap/cache
```

#### API Documentation Not Loading
```bash
# Regenerate Swagger documentation
php artisan l5-swagger:generate
```

#### Export Not Working
```bash
# Clear config cache
php artisan config:clear
# Check if DocumentFieldsService is working
php artisan tinker
app(App\Services\DocumentFieldsService::class)->getAllDocumentTypes();
```

## 📊 Monitoring

### Check System Health:
```bash
# Check if observer is working (new users get plans)
tail -f storage/logs/laravel.log | grep "Free plan assigned"

# Check document processing
tail -f storage/logs/laravel.log | grep "Document processing"

# Check API usage
tail -f storage/logs/laravel.log | grep "API"
```

### Database Monitoring:
```sql
-- Check plan distribution
SELECT p.name, COUNT(up.id) as user_count 
FROM plans p 
LEFT JOIN user_plans up ON p.id = up.plan_id AND up.is_active = 1 
GROUP BY p.id;

-- Check users without plans
SELECT COUNT(*) as users_without_plans 
FROM users u 
LEFT JOIN user_plans up ON u.id = up.user_id AND up.is_active = 1 
WHERE up.id IS NULL;
```

## 🔄 Rollback Plan

If you need to rollback:

### 1. Revert Code
```bash
git log --oneline -10  # Find previous commit
git reset --hard <previous-commit-hash>
```

### 2. Rollback Database (if needed)
```bash
# Only if you need to undo migrations
php artisan migrate:rollback --step=2
```

### 3. Clear Caches
```bash
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

## 📞 Support

If you encounter issues during deployment:

1. Check the logs: `storage/logs/laravel.log`
2. Verify environment configuration
3. Ensure database connectivity
4. Check file permissions
5. Verify web server configuration

For additional support, check the troubleshooting guide or contact the development team.
