<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'monthly_credits',
        'price_lkr',
        'max_team_members',
        'api_rate_limit_per_minute',
        'features',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'features' => 'array',
        'is_active' => 'boolean',
        'price_lkr' => 'decimal:2',
    ];

    public function userPlans(): HasMany
    {
        return $this->hasMany(UserPlan::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price_lkr');
    }

    public function getFormattedPriceAttribute(): string
    {
        if ($this->price_lkr == 0) {
            return 'Free';
        }

        return 'LKR ' . number_format($this->price_lkr, 2);
    }

    public function isFree(): bool
    {
        return $this->price_lkr == 0;
    }

    public function isPro(): bool
    {
        return $this->slug === 'pro';
    }

    public function isEnterprise(): bool
    {
        return $this->slug === 'enterprise';
    }
}
