<?php

namespace App\Http\Controllers;

use App\Models\DemoRequest;
use App\Models\DocumentType;
use App\Services\OcrService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DemoController extends Controller
{
    protected $ocrService;

    public function __construct(OcrService $ocrService)
    {
        $this->ocrService = $ocrService;
    }

    public function process(Request $request)
    {
        try {
            // Check demo rate limit (1 per IP per day)
            $clientIp = $request->ip();
            $dailyLimit = config('app.demo_daily_limit', 1);

            $todayCount = DemoRequest::byIp($clientIp)->today()->count();

            if ($todayCount >= $dailyLimit) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'DEMO_LIMIT_EXCEEDED',
                        'message' => "Demo limit exceeded. You can only process {$dailyLimit} document per day. Please register for unlimited access.",
                        'limit' => $dailyLimit,
                        'count' => $todayCount,
                    ],
                ], 429);
            }

            $request->validate([
                'file' => 'required|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
                'document_type' => 'required|string|exists:document_types,slug',
                'additional_file' => 'nullable|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
            ]);

            $documentType = DocumentType::where('slug', $request->document_type)->first();

            if (!$documentType) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_DOCUMENT_TYPE',
                        'message' => 'Invalid document type selected.',
                    ],
                ], 400);
            }

            // Validate file types
            $this->validateDemoFile($request->file('file'), $documentType);

            if ($request->hasFile('additional_file')) {
                $this->validateDemoFile($request->file('additional_file'), $documentType);
            }

            // Create demo request record
            $demoRequest = DemoRequest::create([
                'ip_address' => $clientIp,
                'user_agent' => $request->userAgent(),
                'document_type' => $request->document_type,
                'original_filename' => $request->file('file')->getClientOriginalName(),
                'additional_filename' => $request->hasFile('additional_file') ? $request->file('additional_file')->getClientOriginalName() : null,
                'file_size_bytes' => $request->file('file')->getSize(),
                'mime_type' => $request->file('file')->getMimeType(),
                'status' => 'processing',
                'request_data' => [
                    'document_type' => $request->document_type,
                    'has_additional_file' => $request->hasFile('additional_file'),
                ],
            ]);

            // Simulate processing delay
            sleep(2);

            // Generate demo results
            $demoResults = $this->generateDemoResults($request->document_type);
            $processingTime = 2000; // Simulated 2 seconds

            // Update demo request record
            $demoRequest->update([
                'status' => 'completed',
                'response_data' => $demoResults,
                'processing_time_ms' => $processingTime,
                'confidence_score' => rand(85, 98) / 100,
                'processed_at' => now(),
            ]);

            Log::info('Demo processing completed', [
                'ip' => $clientIp,
                'document_type' => $request->document_type,
                'file_name' => $request->file('file')->getClientOriginalName(),
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'status' => 'completed',
                    'document_type' => $request->document_type,
                    'confidence_score' => rand(85, 98) / 100,
                    'processing_time_ms' => rand(1500, 3000),
                    'result' => $demoResults,
                    'demo_mode' => true,
                    'message' => 'This is a demo result. Register for real OCR processing.',
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Demo processing failed', [
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'DEMO_PROCESSING_FAILED',
                    'message' => $e->getMessage(),
                ],
            ], 400);
        }
    }

    private function validateDemoFile($file, $documentType)
    {
        // Check file size
        $maxSizeBytes = $documentType->max_file_size_mb * 1024 * 1024;
        if ($file->getSize() > $maxSizeBytes) {
            throw new \Exception("File size exceeds maximum allowed size of {$documentType->max_file_size_mb}MB");
        }

        // Check file extension
        $allowedExtensions = $documentType->allowed_extensions;
        $fileExtension = strtolower($file->getClientOriginalExtension());

        if (!in_array($fileExtension, $allowedExtensions)) {
            throw new \Exception("File type not allowed. Allowed types: " . implode(', ', $allowedExtensions));
        }

        // Check if file is valid
        if (!$file->isValid()) {
            throw new \Exception("Invalid file upload");
        }
    }

    private function generateDemoResults(string $documentType): array
    {
        switch ($documentType) {
            case 'nic':
                return [
                    'nic_number' => $this->generateFakeNic(),
                    'full_name' => 'DEMO USER SILVA',
                    'date_of_birth' => '1990-01-15',
                    'address' => '123 Demo Street, Colombo 01',
                    'gender' => 'Male',
                ];

            case 'driving_license':
                return [
                    'license_number' => 'B' . rand(1000000, 9999999),
                    'full_name' => 'DEMO USER PERERA',
                    'expiry_date' => '2026-12-31',
                    'vehicle_classes' => ['B1', 'A1'],
                    'issued_date' => '2020-01-15',
                ];

            case 'vehicle_registration':
                return [
                    'registration_number' => $this->generateVehicleNumber(),
                    'vehicle_make' => 'Toyota',
                    'vehicle_model' => 'Corolla',
                    'year' => '2020',
                    'engine_number' => 'ENG' . rand(100000, 999999),
                    'chassis_number' => 'CHS' . rand(100000, 999999),
                ];

            case 'land_deed':
                return [
                    'deed_number' => 'DEED' . rand(10000, 99999),
                    'property_address' => '456 Demo Property Lane, Kandy',
                    'owner_name' => 'DEMO PROPERTY OWNER',
                    'extent' => '2 Acres 1 Rood 20 Perches',
                    'survey_plan_number' => 'SP' . rand(1000, 9999),
                ];

            case 'invoice':
                return [
                    'invoice_number' => 'DEMO-INV-' . rand(1000, 9999),
                    'total_amount' => 'LKR ' . number_format(rand(1000, 50000), 2),
                    'vendor_name' => 'Demo Company (Pvt) Ltd',
                    'invoice_date' => now()->format('Y-m-d'),
                    'due_date' => now()->addDays(30)->format('Y-m-d'),
                    'items' => [
                        ['description' => 'Demo Product 1', 'amount' => 'LKR 15,000.00'],
                        ['description' => 'Demo Service 2', 'amount' => 'LKR 10,000.00'],
                    ],
                ];

            default:
                return [
                    'text_content' => 'Demo extracted text content from the document',
                    'confidence' => rand(80, 95) / 100,
                    'document_type' => $documentType,
                ];
        }
    }

    private function generateFakeNic(): string
    {
        $year = rand(1950, 2000);
        $dayOfYear = str_pad(rand(1, 365), 3, '0', STR_PAD_LEFT);
        $suffix = rand(0, 1) ? 'V' : 'X';

        return $year . $dayOfYear . rand(10000, 99999) . $suffix;
    }

    private function generateVehicleNumber(): string
    {
        $provinces = ['WP', 'CP', 'SP', 'NP', 'EP', 'NC', 'NW', 'UVA', 'SAB'];
        $province = $provinces[array_rand($provinces)];
        $letters = chr(rand(65, 90)) . chr(rand(65, 90));
        $numbers = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $province . '-' . $letters . '-' . $numbers;
    }

    public function checkLimit(Request $request)
    {
        $clientIp = $request->ip();
        $dailyLimit = config('app.demo_daily_limit', 1);
        $todayCount = DemoRequest::byIp($clientIp)->today()->count();

        return response()->json([
            'success' => true,
            'data' => [
                'limit_reached' => $todayCount >= $dailyLimit,
                'daily_limit' => $dailyLimit,
                'today_count' => $todayCount,
                'remaining' => max(0, $dailyLimit - $todayCount),
                'reset_time' => now()->endOfDay()->toISOString(),
            ],
        ]);
    }
}
