<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Models\DocumentType;
use App\Services\DocumentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DocumentController extends Controller
{
    protected $documentService;

    public function __construct(DocumentService $documentService)
    {
        $this->documentService = $documentService;
    }

    public function upload(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
                'document_type' => 'required|string|exists:document_types,slug',
                'additional_file' => 'nullable|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
                'webhook_url' => 'nullable|url',
            ]);

            $user = $request->user();
            $documentType = DocumentType::where('slug', $request->document_type)->first();

            // Check if user has enough credits
            if (!$user->hasCredits($documentType->credit_cost)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INSUFFICIENT_CREDITS',
                        'message' => "Not enough credits. Required: {$documentType->credit_cost}, Available: {$user->remaining_credits}",
                        'details' => [
                            'required_credits' => $documentType->credit_cost,
                            'available_credits' => $user->remaining_credits,
                        ],
                    ],
                ], 402);
            }

            $document = $this->documentService->uploadDocument(
                $user,
                $request->file('file'),
                $request->document_type,
                $request->file('additional_file'),
                $request->webhook_url
            );

            // Start processing
            $this->documentService->processDocument($document);

            return response()->json([
                'success' => true,
                'data' => [
                    'document_id' => $document->uuid,
                    'status' => $document->status,
                    'credits_used' => $document->credits_used,
                    'estimated_processing_time' => $documentType->processing_time_estimate_seconds,
                    'expires_at' => $document->expires_at->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Document upload failed', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'UPLOAD_FAILED',
                    'message' => $e->getMessage(),
                ],
            ], 400);
        }
    }

    public function show(Request $request, string $uuid)
    {
        $user = $request->user();
        $document = Document::where('uuid', $uuid)
                           ->where('user_id', $user->id)
                           ->first();

        if (!$document) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'DOCUMENT_NOT_FOUND',
                    'message' => 'Document not found',
                ],
            ], 404);
        }

        if ($document->isExpired()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'DOCUMENT_EXPIRED',
                    'message' => 'Document has expired',
                ],
            ], 410);
        }

        if ($document->isCompleted()) {
            $result = $this->documentService->getDocumentResult($document);
            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        }

        if ($document->isFailed()) {
            return response()->json([
                'success' => false,
                'data' => [
                    'document_id' => $document->uuid,
                    'status' => $document->status,
                    'error_message' => $document->error_message,
                ],
            ]);
        }

        // Still processing
        return response()->json([
            'success' => true,
            'data' => [
                'document_id' => $document->uuid,
                'status' => $document->status,
                'estimated_completion' => $document->created_at
                    ->addSeconds($document->documentType->processing_time_estimate_seconds)
                    ->toISOString(),
            ],
        ]);
    }

    public function index(Request $request)
    {
        $request->validate([
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|string|in:pending,processing,completed,failed,manual_review',
            'document_type' => 'nullable|string|exists:document_types,slug',
        ]);

        $user = $request->user();
        $documents = $this->documentService->getUserDocuments($user, $request->all());

        return response()->json([
            'success' => true,
            'data' => [
                'documents' => $documents->items(),
                'pagination' => [
                    'current_page' => $documents->currentPage(),
                    'per_page' => $documents->perPage(),
                    'total' => $documents->total(),
                    'last_page' => $documents->lastPage(),
                ],
            ],
        ]);
    }
}
