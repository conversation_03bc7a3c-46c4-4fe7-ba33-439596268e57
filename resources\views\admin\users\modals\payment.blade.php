<!-- Payment Recording Modal -->
<div id="paymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Record Offline Payment</h3>
                <button onclick="hidePaymentModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="paymentForm" class="space-y-4">
                @csrf
                <input type="hidden" id="payment_user_id" name="user_id">
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="payment_amount" class="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                        <input type="number" id="payment_amount" name="amount" required min="0" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label for="payment_currency" class="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                        <select id="payment_currency" name="currency" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="LKR">LKR</option>
                            <option value="USD">USD</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <label for="payment_plan" class="block text-sm font-medium text-gray-700 mb-1">Plan to Activate</label>
                    <select id="payment_plan" name="plan_slug" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        @foreach($plans ?? [] as $plan)
                            <option value="{{ $plan->slug }}">
                                {{ $plan->name }}
                                @if($plan->price_lkr > 0)
                                    (LKR {{ number_format($plan->price_lkr, 0) }}/month)
                                @endif
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label for="payment_duration" class="block text-sm font-medium text-gray-700 mb-1">Duration (Months)</label>
                    <select id="payment_duration" name="duration_months" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="1">1 Month</option>
                        <option value="3">3 Months</option>
                        <option value="6">6 Months</option>
                        <option value="12">12 Months</option>
                        <option value="24">24 Months</option>
                    </select>
                </div>
                
                <div>
                    <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                    <select id="payment_method" name="payment_method" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="cash">Cash</option>
                        <option value="check">Check</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <div>
                    <label for="payment_reference" class="block text-sm font-medium text-gray-700 mb-1">Reference/Transaction ID</label>
                    <input type="text" id="payment_reference" name="reference" 
                           placeholder="e.g., TXN123456, Check #1234"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="payment_notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea id="payment_notes" name="notes" rows="3" 
                              placeholder="Additional notes about this payment..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-md p-3">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-700">
                                This will record the payment and immediately activate the selected plan for the specified duration.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hidePaymentModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700">
                        <i class="fas fa-dollar-sign mr-2"></i>
                        Record Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
