<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\V1\DocumentController;
use App\Http\Controllers\Api\UsageController;
use App\Models\DocumentType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Public routes
Route::prefix('v1')->group(function () {
    // Authentication
    Route::post('/auth/login', [AuthController::class, 'login']);

    // Document types (public)
    Route::get('/document-types', function () {
        $documentTypes = DocumentType::active()->get()->map(function ($type) {
            return [
                'slug' => $type->slug,
                'name' => $type->name,
                'description' => $type->description,
                'credit_cost' => $type->credit_cost,
                'max_file_size_mb' => $type->max_file_size_mb,
                'supports_additional_file' => $type->supports_additional_file,
                'allowed_extensions' => $type->allowed_extensions,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $documentTypes,
        ]);
    });
});

// Protected routes
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // Authentication
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/user', [AuthController::class, 'user']);

    // Documents
    Route::post('/documents/upload', [DocumentController::class, 'upload']);
    Route::get('/documents/{uuid}', [DocumentController::class, 'show']);
    Route::get('/documents', [DocumentController::class, 'index']);

    // Usage & Credits
    Route::get('/usage', [UsageController::class, 'index']);
    Route::get('/usage/history', [UsageController::class, 'history']);
});
