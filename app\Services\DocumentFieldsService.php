<?php

namespace App\Services;

class DocumentFieldsService
{
    /**
     * Get all document types and their field configurations
     */
    public function getAllDocumentTypes(): array
    {
        return config('document-fields');
    }

    /**
     * Get field configuration for a specific document type
     */
    public function getFieldsForDocumentType(string $documentType): array
    {
        $config = config('document-fields.' . $documentType);
        return $config ? $config['fields'] : [];
    }

    /**
     * Get display name for a document type
     */
    public function getDocumentTypeName(string $documentType): string
    {
        $config = config('document-fields.' . $documentType);
        return $config ? $config['name'] : ucfirst(str_replace('_', ' ', $documentType));
    }

    /**
     * Get validation rules for a document type
     */
    public function getValidationRules(string $documentType): array
    {
        $fields = $this->getFieldsForDocumentType($documentType);
        $rules = [];

        foreach ($fields as $fieldName => $fieldConfig) {
            if (isset($fieldConfig['validation'])) {
                $rules[$fieldName] = $fieldConfig['validation'];
            }
        }

        return $rules;
    }

    /**
     * Get required fields for a document type
     */
    public function getRequiredFields(string $documentType): array
    {
        $fields = $this->getFieldsForDocumentType($documentType);
        $required = [];

        foreach ($fields as $fieldName => $fieldConfig) {
            if ($fieldConfig['required'] ?? false) {
                $required[] = $fieldName;
            }
        }

        return $required;
    }

    /**
     * Get export headers for a document type (ordered by export_order)
     */
    public function getExportHeaders(string $documentType): array
    {
        $fields = $this->getFieldsForDocumentType($documentType);
        
        // Sort by export_order
        uasort($fields, function ($a, $b) {
            return ($a['export_order'] ?? 999) <=> ($b['export_order'] ?? 999);
        });

        $headers = [];
        foreach ($fields as $fieldName => $fieldConfig) {
            $headers[$fieldName] = $fieldConfig['label'];
        }

        // Add metadata fields
        $headers['document_filename'] = 'Document Filename';
        $headers['processed_date'] = 'Processed Date';
        $headers['confidence_score'] = 'Confidence Score';

        return $headers;
    }

    /**
     * Format extracted data for export
     */
    public function formatForExport(string $documentType, array $extractedData, array $metadata = []): array
    {
        $fields = $this->getFieldsForDocumentType($documentType);
        $formatted = [];

        // Sort by export_order
        uasort($fields, function ($a, $b) {
            return ($a['export_order'] ?? 999) <=> ($b['export_order'] ?? 999);
        });

        // Add field values in order
        foreach ($fields as $fieldName => $fieldConfig) {
            $value = $extractedData[$fieldName] ?? '';
            
            // Format based on field type
            switch ($fieldConfig['type']) {
                case 'date':
                    if ($value && $value !== '') {
                        try {
                            $formatted[$fieldName] = \Carbon\Carbon::parse($value)->format('Y-m-d');
                        } catch (\Exception $e) {
                            $formatted[$fieldName] = $value;
                        }
                    } else {
                        $formatted[$fieldName] = '';
                    }
                    break;
                case 'decimal':
                    $formatted[$fieldName] = is_numeric($value) ? number_format((float)$value, 2) : $value;
                    break;
                default:
                    $formatted[$fieldName] = $value;
            }
        }

        // Add metadata
        $formatted['document_filename'] = $metadata['filename'] ?? '';
        $formatted['processed_date'] = $metadata['processed_date'] ?? '';
        $formatted['confidence_score'] = $metadata['confidence_score'] ?? '';

        return $formatted;
    }

    /**
     * Validate extracted data against field configuration
     */
    public function validateExtractedData(string $documentType, array $data): array
    {
        $rules = $this->getValidationRules($documentType);
        $errors = [];

        $validator = \Validator::make($data, $rules);
        
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
        }

        return $errors;
    }

    /**
     * Get field labels for display
     */
    public function getFieldLabels(string $documentType): array
    {
        $fields = $this->getFieldsForDocumentType($documentType);
        $labels = [];

        foreach ($fields as $fieldName => $fieldConfig) {
            $labels[$fieldName] = $fieldConfig['label'];
        }

        return $labels;
    }

    /**
     * Get sample data for a document type (for testing/demo)
     */
    public function getSampleData(string $documentType): array
    {
        $sampleData = [
            'nic' => [
                'nic_number' => '123456789V',
                'full_name' => 'John Doe Silva',
                'name_with_initials' => 'J.D. Silva',
                'date_of_birth' => '1990-01-15',
                'gender' => 'Male',
                'address' => '123 Main Street, Colombo 01'
            ],
            'driving_license' => [
                'license_number' => 'DL123456',
                'full_name' => 'Jane Smith',
                'date_of_birth' => '1985-05-20',
                'issue_date' => '2020-01-01',
                'expiry_date' => '2025-01-01',
                'vehicle_classes' => 'A, B1',
                'address' => '456 Second Street, Kandy'
            ],
            'vehicle_registration' => [
                'registration_number' => 'ABC-1234',
                'vehicle_make' => 'Toyota',
                'vehicle_model' => 'Corolla',
                'year_of_manufacture' => 2020,
                'engine_number' => 'ENG123456',
                'chassis_number' => 'CHS789012',
                'owner_name' => 'Robert Johnson'
            ],
            'land_deed' => [
                'deed_number' => 'DEED-2023-001',
                'property_address' => '789 Property Lane, Galle',
                'owner_name' => 'Mary Fernando',
                'land_extent' => '2 Acres 1 Rood 20 Perches',
                'survey_plan_number' => 'SP-12345',
                'registration_date' => '2023-01-15'
            ],
            'invoice' => [
                'invoice_number' => 'INV-2024-001',
                'vendor_name' => 'ABC Company Ltd',
                'vendor_address' => '123 Business Street, Colombo 03',
                'invoice_date' => '2024-01-15',
                'due_date' => '2024-02-15',
                'total_amount' => 15000.00,
                'currency' => 'LKR',
                'tax_amount' => 2250.00
            ]
        ];

        return $sampleData[$documentType] ?? [];
    }
}
