<?php

namespace App\Services;

use App\Models\Plan;
use App\Models\User;
use App\Models\UserPlan;
use Carbon\Carbon;

class PlanService
{
    /**
     * Assign the free plan to a new user
     */
    public function assignFreePlanToUser(User $user): UserPlan
    {
        $freePlan = Plan::where('slug', 'free')->first();
        
        if (!$freePlan) {
            throw new \Exception('Free plan not found. Please run the database seeders.');
        }

        // Check if user already has a plan
        $existingPlan = $user->currentPlan;
        if ($existingPlan) {
            return $existingPlan;
        }

        // Create user plan
        $userPlan = UserPlan::create([
            'user_id' => $user->id,
            'plan_id' => $freePlan->id,
            'credits_remaining' => $freePlan->monthly_credits,
            'starts_at' => now(),
            'expires_at' => null, // Free plan doesn't expire
            'is_active' => true,
        ]);

        // Update user's remaining credits
        $user->update([
            'remaining_credits' => $freePlan->monthly_credits,
        ]);

        return $userPlan;
    }

    /**
     * Upgrade user to a paid plan
     */
    public function upgradeUserPlan(User $user, string $planSlug, ?Carbon $expiresAt = null): UserPlan
    {
        $plan = Plan::where('slug', $planSlug)->first();
        
        if (!$plan) {
            throw new \Exception('Plan not found: ' . $planSlug);
        }

        // Deactivate current plan
        if ($user->currentPlan) {
            $user->currentPlan->update(['is_active' => false]);
        }

        // Create new user plan
        $userPlan = UserPlan::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'credits_remaining' => $plan->monthly_credits,
            'starts_at' => now(),
            'expires_at' => $expiresAt ?? now()->addMonth(),
            'is_active' => true,
        ]);

        // Update user's remaining credits
        $user->update([
            'remaining_credits' => $plan->monthly_credits,
        ]);

        return $userPlan;
    }

    /**
     * Renew user's current plan
     */
    public function renewUserPlan(User $user, int $months = 1): UserPlan
    {
        $currentPlan = $user->currentPlan;
        
        if (!$currentPlan) {
            throw new \Exception('User has no active plan to renew.');
        }

        // Extend expiry date
        $newExpiryDate = $currentPlan->expires_at 
            ? $currentPlan->expires_at->addMonths($months)
            : now()->addMonths($months);

        $currentPlan->update([
            'expires_at' => $newExpiryDate,
        ]);

        // Reset credits to plan's monthly allocation
        $user->update([
            'remaining_credits' => $currentPlan->plan->monthly_credits,
        ]);

        return $currentPlan;
    }

    /**
     * Add credits to user (for offline payments or bonuses)
     */
    public function addCreditsToUser(User $user, int $credits): void
    {
        $user->increment('remaining_credits', $credits);
    }

    /**
     * Check if user's plan has expired and handle accordingly
     */
    public function checkAndHandleExpiredPlan(User $user): void
    {
        $currentPlan = $user->currentPlan;
        
        if (!$currentPlan || !$currentPlan->expires_at) {
            return; // No plan or plan doesn't expire
        }

        if ($currentPlan->expires_at->isPast()) {
            // Plan has expired - downgrade to free plan
            $this->downgradeToFreePlan($user);
        }
    }

    /**
     * Downgrade user to free plan
     */
    public function downgradeToFreePlan(User $user): UserPlan
    {
        // Deactivate current plan
        if ($user->currentPlan) {
            $user->currentPlan->update(['is_active' => false]);
        }

        // Assign free plan
        return $this->assignFreePlanToUser($user);
    }

    /**
     * Get plan usage statistics for a user
     */
    public function getUserPlanUsage(User $user): array
    {
        $currentPlan = $user->currentPlan;
        
        if (!$currentPlan) {
            return [
                'plan_name' => 'No Plan',
                'credits_used' => 0,
                'credits_remaining' => 0,
                'credits_total' => 0,
                'usage_percentage' => 0,
                'expires_at' => null,
                'days_remaining' => null,
            ];
        }

        $creditsTotal = $currentPlan->plan->monthly_credits;
        $creditsRemaining = $user->remaining_credits;
        $creditsUsed = $creditsTotal - $creditsRemaining;
        $usagePercentage = $creditsTotal > 0 ? round(($creditsUsed / $creditsTotal) * 100, 1) : 0;

        $daysRemaining = null;
        if ($currentPlan->expires_at) {
            $daysRemaining = now()->diffInDays($currentPlan->expires_at, false);
            if ($daysRemaining < 0) {
                $daysRemaining = 0;
            }
        }

        return [
            'plan_name' => $currentPlan->plan->name,
            'credits_used' => $creditsUsed,
            'credits_remaining' => $creditsRemaining,
            'credits_total' => $creditsTotal,
            'usage_percentage' => $usagePercentage,
            'expires_at' => $currentPlan->expires_at,
            'days_remaining' => $daysRemaining,
        ];
    }

    /**
     * Record an offline payment and extend plan
     */
    public function recordOfflinePayment(User $user, float $amount, string $planSlug, int $months = 1, ?string $notes = null): UserPlan
    {
        // TODO: Create a payments table to record this transaction
        // For now, just upgrade/renew the plan
        
        $plan = Plan::where('slug', $planSlug)->first();
        
        if (!$plan) {
            throw new \Exception('Plan not found: ' . $planSlug);
        }

        // If user has the same plan, renew it. Otherwise, upgrade.
        if ($user->currentPlan && $user->currentPlan->plan->slug === $planSlug) {
            return $this->renewUserPlan($user, $months);
        } else {
            $expiresAt = now()->addMonths($months);
            return $this->upgradeUserPlan($user, $planSlug, $expiresAt);
        }
    }
}
