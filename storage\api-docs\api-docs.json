{"openapi": "3.0.0", "info": {"title": "SmartOCR.lk API", "description": "API for SmartOCR.lk - Intelligent document processing for Sri Lankan businesses", "contact": {"name": "SmartOCR Support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://smartocr.multiblity.com/api/v1", "description": "Live Server"}], "paths": {"/documents/upload": {"post": {"tags": ["Documents"], "summary": "Upload a document for OCR processing", "description": "Upload a document file for OCR processing. Supports NIC, driving license, vehicle registration, land deed, and invoice.", "operationId": "uploadDocument", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"file": {"description": "Document file to upload (JPG, PNG, PDF)", "type": "string", "format": "binary"}, "document_type": {"description": "Type of document (nic, driving_license, vehicle_registration, land_deed, invoice)", "type": "string", "example": "nic"}, "additional_file": {"description": "Additional file (e.g., back of NIC) - optional", "type": "string", "format": "binary"}, "webhook_url": {"description": "URL to receive webhook notification when processing is complete - optional", "type": "string", "format": "url", "example": "https://your-app.com/webhook"}}, "type": "object"}}}}, "responses": {"200": {"description": "Document uploaded successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"document_id": {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************"}, "status": {"type": "string", "example": "pending"}, "credits_used": {"type": "integer", "example": 1}, "estimated_processing_time": {"type": "integer", "example": 30}, "expires_at": {"type": "string", "format": "date-time", "example": "2024-01-16T10:30:00.000000Z"}}, "type": "object"}}, "type": "object"}}}}, "400": {"description": "Invalid input", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "error": {"properties": {"code": {"type": "string", "example": "VALIDATION_ERROR"}, "message": {"type": "string", "example": "The file field is required."}}, "type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Documents", "description": "Documents"}]}