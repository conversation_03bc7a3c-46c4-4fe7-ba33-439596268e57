# Deployment Guide

## Overview
Step-by-step deployment instructions for SmartOCR.lk platform across different environments.

## Prerequisites

### System Requirements
- **PHP**: 8.2 or higher
- **MySQL**: 8.0 or higher
- **Redis**: 6.0 or higher
- **Node.js**: 18.x or higher
- **Composer**: 2.x
- **Web Server**: Nginx or Apache

### Server Specifications

#### Development
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 50GB SSD
- **Bandwidth**: 100Mbps

#### Production
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 200GB+ SSD
- **Bandwidth**: 1Gbps+

## Environment Setup

### 1. Development Environment

#### Local Setup with Laravel Sail
```bash
# Clone repository
git clone https://github.com/your-org/smartocr-lk.git
cd smartocr-lk

# Install dependencies
composer install
npm install

# Setup environment
cp .env.example .env
php artisan key:generate

# Configure database
php artisan migrate
php artisan db:seed

# Start development server
php artisan serve
npm run dev
```

#### Manual Local Setup
```bash
# Install PHP dependencies
composer install

# Install Node dependencies
npm install && npm run build

# Database setup
mysql -u root -p
CREATE DATABASE smartocr_dev;
CREATE USER 'smartocr'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON smartocr_dev.* TO 'smartocr'@'localhost';

# Environment configuration
cp .env.example .env
```

**.env Configuration:**
```env
APP_NAME="SmartOCR.lk"
APP_ENV=local
APP_KEY=base64:generated-key
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=smartocr_dev
DB_USERNAME=smartocr
DB_PASSWORD=password

CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025

# OCR Service Configuration
OCR_SERVICE_URL=http://localhost:8001/api/process
OCR_SERVICE_API_KEY=dev-key
OCR_SERVICE_TIMEOUT=30

# File Storage
FILESYSTEM_DISK=local
DOCUMENT_STORAGE_PATH=documents
DOCUMENT_RETENTION_HOURS=24

# Rate Limiting
RATE_LIMIT_FREE=10
RATE_LIMIT_PRO=60
RATE_LIMIT_ENTERPRISE=200
```

### 2. Staging Environment

#### Server Setup (Ubuntu 22.04)
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.2
sudo add-apt-repository ppa:ondrej/php
sudo apt install php8.2 php8.2-fpm php8.2-mysql php8.2-redis \
    php8.2-gd php8.2-curl php8.2-mbstring php8.2-xml \
    php8.2-zip php8.2-bcmath php8.2-intl

# Install MySQL
sudo apt install mysql-server
sudo mysql_secure_installation

# Install Redis
sudo apt install redis-server
sudo systemctl enable redis-server

# Install Nginx
sudo apt install nginx
sudo systemctl enable nginx

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### Application Deployment
```bash
# Create application directory
sudo mkdir -p /var/www/smartocr-staging
sudo chown $USER:www-data /var/www/smartocr-staging

# Clone and setup
cd /var/www/smartocr-staging
git clone https://github.com/your-org/smartocr-lk.git .
composer install --no-dev --optimize-autoloader
npm ci && npm run build

# Set permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache

# Environment setup
cp .env.staging .env
php artisan key:generate
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/smartocr-staging
server {
    listen 80;
    server_name staging.smartocr.lk;
    root /var/www/smartocr-staging/public;
    index index.php;

    client_max_body_size 20M;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

### 3. Production Environment

#### Infrastructure Setup
```bash
# Production server setup (similar to staging but with SSL)
sudo certbot --nginx -d smartocr.lk -d api.smartocr.lk

# Database optimization
sudo mysql -u root -p
SET GLOBAL innodb_buffer_pool_size = 2G;
SET GLOBAL query_cache_size = 256M;
```

#### Production Nginx Configuration
```nginx
# /etc/nginx/sites-available/smartocr-production
server {
    listen 443 ssl http2;
    server_name smartocr.lk www.smartocr.lk;
    root /var/www/smartocr/public;
    index index.php;

    ssl_certificate /etc/letsencrypt/live/smartocr.lk/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/smartocr.lk/privkey.pem;

    client_max_body_size 20M;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Security
        fastcgi_hide_header X-Powered-By;
    }

    # Static file caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";