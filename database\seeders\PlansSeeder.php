<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Free',
                'slug' => 'free',
                'description' => 'Perfect for trying out our service',
                'monthly_credits' => 50,
                'price_lkr' => 0.00,
                'max_team_members' => 1,
                'api_rate_limit_per_minute' => 10,
                'features' => json_encode(['Basic OCR', 'Email Support']),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Pro',
                'slug' => 'pro',
                'description' => 'For small businesses and professionals',
                'monthly_credits' => 1000,
                'price_lkr' => 10000.00,
                'max_team_members' => 5,
                'api_rate_limit_per_minute' => 60,
                'features' => json_encode(['Advanced OCR', 'API Access', 'Priority Support', 'Team Collaboration']),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Enterprise',
                'slug' => 'enterprise',
                'description' => 'For large organizations',
                'monthly_credits' => 5000,
                'price_lkr' => 25000.00,
                'max_team_members' => 50,
                'api_rate_limit_per_minute' => 200,
                'features' => json_encode(['Premium OCR', 'Custom Integration', 'Dedicated Support', 'Advanced Analytics']),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('plans')->insert($plans);
    }
}
