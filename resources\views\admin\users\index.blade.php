@extends('layouts.dashboard')

@section('title', 'Company Management')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Company Management</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage companies, their plans, billing, and usage</p>
                </div>
                <div class="flex space-x-3">
                    <button onclick="exportUsers()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition">
                        <i class="fas fa-download mr-2"></i>
                        Export Data
                    </button>
                    <a href="{{ route('admin.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" 
                           placeholder="Name or email..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="plan" class="block text-sm font-medium text-gray-700 mb-1">Plan</label>
                    <select name="plan" id="plan" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Plans</option>
                        @foreach($plans as $plan)
                            <option value="{{ $plan->slug }}" {{ request('plan') === $plan->slug ? 'selected' : '' }}>
                                {{ $plan->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Expired</option>
                        <option value="no_plan" {{ request('status') === 'no_plan' ? 'selected' : '' }}>No Plan</option>
                    </select>
                </div>
                
                <div class="flex items-end space-x-2">
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                    @if(request()->hasAny(['search', 'plan', 'status']))
                    <a href="{{ route('admin.users.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition">
                        Clear
                    </a>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan & Billing</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage & Credits</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API & Teams</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($users as $user)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <span class="text-sm font-medium text-blue-600">{{ substr($user->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                    <div class="text-xs text-gray-400">
                                        Joined {{ $user->created_at->format('M j, Y') }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        
                        <td class="px-6 py-4">
                            @php
                                $plan = $user->currentPlan;
                                $isExpired = $plan && $plan->expires_at && $plan->expires_at->isPast();
                                $isExpiringSoon = $plan && $plan->expires_at && $plan->expires_at->diffInDays() <= 7 && !$isExpired;
                            @endphp
                            
                            <div class="space-y-1">
                                @if($plan)
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full 
                                        @if($isExpired) bg-red-100 text-red-800
                                        @elseif($isExpiringSoon) bg-yellow-100 text-yellow-800
                                        @elseif($plan->plan->slug === 'free') bg-gray-100 text-gray-800
                                        @else bg-green-100 text-green-800 @endif">
                                        {{ $plan->plan->name }}
                                    </span>
                                    
                                    @if($plan->expires_at)
                                        <div class="text-xs text-gray-500">
                                            @if($isExpired)
                                                <span class="text-red-600">⚠️ Expired {{ $plan->expires_at->diffForHumans() }}</span>
                                            @elseif($isExpiringSoon)
                                                <span class="text-yellow-600">⏰ Expires in {{ $plan->expires_at->diffInDays() }} days</span>
                                            @else
                                                Expires {{ $plan->expires_at->format('M j, Y') }}
                                            @endif
                                        </div>
                                    @else
                                        <div class="text-xs text-gray-500">Never expires</div>
                                    @endif
                                @else
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">No Plan</span>
                                @endif
                            </div>
                        </td>
                        
                        <td class="px-6 py-4">
                            <div class="space-y-1">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-900">{{ number_format($user->remaining_credits) }}</span>
                                    <span class="text-xs text-gray-500">credits</span>
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    📄 {{ number_format($user->documents->count()) }} total docs
                                </div>
                                <div class="text-xs text-gray-500">
                                    📅 {{ number_format($user->documents->where('created_at', '>=', now()->subDays(30))->count()) }} this month
                                </div>
                            </div>
                        </td>
                        
                        <td class="px-6 py-4">
                            <div class="space-y-1">
                                <div class="text-sm text-gray-900">
                                    🔑 {{ $user->tokens->count() }} API tokens
                                </div>
                                <div class="text-sm text-gray-900">
                                    👥 {{ $user->teams->count() }} teams
                                </div>
                                @php
                                    $lastUsedToken = $user->tokens->where('last_used_at', '!=', null)->sortByDesc('last_used_at')->first();
                                @endphp
                                @if($lastUsedToken)
                                    <div class="text-xs text-green-600">
                                        Last API use: {{ $lastUsedToken->last_used_at->diffForHumans() }}
                                    </div>
                                @endif
                            </div>
                        </td>
                        
                        <td class="px-6 py-4">
                            @if($user->email_verified_at)
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Active
                                </span>
                            @else
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Unverified
                                </span>
                            @endif
                        </td>
                        
                        <td class="px-6 py-4">
                            <div class="flex space-x-1">
                                <button onclick="viewUser({{ $user->id }})" class="text-blue-600 hover:text-blue-900 p-1" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button onclick="editCredits({{ $user->id }})" class="text-green-600 hover:text-green-900 p-1" title="Edit Credits">
                                    <i class="fas fa-coins"></i>
                                </button>
                                <button onclick="managePlan({{ $user->id }})" class="text-purple-600 hover:text-purple-900 p-1" title="Manage Plan">
                                    <i class="fas fa-credit-card"></i>
                                </button>
                                <button onclick="recordPayment({{ $user->id }})" class="text-yellow-600 hover:text-yellow-900 p-1" title="Record Payment">
                                    <i class="fas fa-dollar-sign"></i>
                                </button>
                                <button onclick="viewUsage({{ $user->id }})" class="text-teal-600 hover:text-teal-900 p-1" title="Usage Logs">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                                @if($user->email !== '<EMAIL>')
                                <button onclick="suspendUser({{ $user->id }})" class="text-red-600 hover:text-red-900 p-1" title="Suspend">
                                    <i class="fas fa-ban"></i>
                                </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <div class="text-gray-500">
                                <i class="fas fa-users text-4xl mb-4"></i>
                                <p class="text-lg">No companies found</p>
                                <p class="text-sm">Try adjusting your search criteria</p>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($users->hasPages())
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            {{ $users->appends(request()->query())->links() }}
        </div>
        @endif
    </div>
</div>

<!-- Include modals -->
@include('admin.users.modals.credits')
@include('admin.users.modals.plan')
@include('admin.users.modals.payment')
@include('admin.users.modals.usage')
@include('admin.users.modals.details')

@push('scripts')
<script src="{{ asset('js/admin/users.js') }}"></script>
@endpush
@endsection
