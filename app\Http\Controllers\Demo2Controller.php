<?php

namespace App\Http\Controllers;

use App\Models\DemoRequest;
use App\Models\DocumentType;
use App\Services\ExternalOcrService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class Demo2Controller extends Controller
{
    protected $externalOcrService;

    public function __construct(ExternalOcrService $externalOcrService)
    {
        $this->externalOcrService = $externalOcrService;
    }

    public function index()
    {
        $documentTypes = DocumentType::active()->get();
        return view('landing.demo2', compact('documentTypes'));
    }

    public function process(Request $request)
    {
        try {
            // Check demo rate limit (1 per IP per day)
            $clientIp = $request->ip();
            $dailyLimit = config('app.demo_daily_limit', 1);

            $todayCount = DemoRequest::byIp($clientIp)->today()->count();

            if ($todayCount >= $dailyLimit) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'DEMO_LIMIT_EXCEEDED',
                        'message' => "Demo limit exceeded. You can only process {$dailyLimit} document per day. Please register for unlimited access.",
                        'limit' => $dailyLimit,
                        'count' => $todayCount,
                    ],
                ], 429);
            }

            $request->validate([
                'file' => 'required|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
                'document_type' => 'required|string|exists:document_types,slug',
                'additional_file' => 'nullable|file|max:' . (config('app.max_file_size_mb', 10) * 1024),
            ]);

            $documentType = DocumentType::where('slug', $request->document_type)->first();

            if (!$documentType) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_DOCUMENT_TYPE',
                        'message' => 'Invalid document type selected.',
                    ],
                ], 400);
            }

            // Validate file types
            $this->validateDemoFile($request->file('file'), $documentType);

            if ($request->hasFile('additional_file')) {
                $this->validateDemoFile($request->file('additional_file'), $documentType);
            }

            // Create demo request record
            $demoRequest = DemoRequest::create([
                'ip_address' => $clientIp,
                'user_agent' => $request->userAgent(),
                'document_type' => $request->document_type,
                'original_filename' => $request->file('file')->getClientOriginalName(),
                'additional_filename' => $request->hasFile('additional_file') ? $request->file('additional_file')->getClientOriginalName() : null,
                'file_size_bytes' => $request->file('file')->getSize(),
                'mime_type' => $request->file('file')->getMimeType(),
                'status' => 'processing',
                'request_data' => [
                    'document_type' => $request->document_type,
                    'has_additional_file' => $request->hasFile('additional_file'),
                ],
            ]);

            // Process with external OCR service
            try {
                $startTime = microtime(true);

                $result = $this->externalOcrService->processDocument(
                    $request->document_type,
                    $request->file('file'),
                    $request->file('additional_file')
                );

                $processingTime = round((microtime(true) - $startTime) * 1000);

                // Update demo request record
                $demoRequest->update([
                    'status' => 'completed',
                    'response_data' => $result,
                    'processing_time_ms' => $processingTime,
                    'confidence_score' => $result['confidence_score'] ?? null,
                    'processed_at' => now(),
                ]);

                return response()->json([
                    'success' => true,
                    'data' => [
                        'status' => 'completed',
                        'document_type' => $request->document_type,
                        'confidence_score' => $result['confidence_score'] ?? 0.85,
                        'processing_time_ms' => $processingTime,
                        'result' => $result['result'],
                        'demo_mode' => true,
                        'message' => 'This is a demo result. Register for real OCR processing.',
                        'demo_request_id' => $demoRequest->id,
                    ],
                ]);

            } catch (\Exception $e) {
                // Update demo request with error
                $demoRequest->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                    'processed_at' => now(),
                ]);

                // For demo, provide simulated results even if external API fails
                $simulatedResults = $this->generateSimulatedResults($request->document_type);

                return response()->json([
                    'success' => true,
                    'data' => [
                        'status' => 'completed',
                        'document_type' => $request->document_type,
                        'confidence_score' => 0.85,
                        'processing_time_ms' => 2000,
                        'result' => $simulatedResults,
                        'demo_mode' => true,
                        'message' => 'This is a simulated demo result. External API error: ' . $e->getMessage(),
                        'demo_request_id' => $demoRequest->id,
                    ],
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Demo processing failed', [
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'DEMO_PROCESSING_FAILED',
                    'message' => $e->getMessage(),
                ],
            ], 400);
        }
    }

    private function validateDemoFile($file, $documentType)
    {
        // Check file size
        $maxSizeBytes = $documentType->max_file_size_mb * 1024 * 1024;
        if ($file->getSize() > $maxSizeBytes) {
            throw new \Exception("File size exceeds maximum allowed size of {$documentType->max_file_size_mb}MB");
        }

        // Check file extension
        $allowedExtensions = $documentType->allowed_extensions;
        $fileExtension = strtolower($file->getClientOriginalExtension());

        if (!in_array($fileExtension, $allowedExtensions)) {
            throw new \Exception("File type not allowed. Allowed types: " . implode(', ', $allowedExtensions));
        }

        // Check if file is valid
        if (!$file->isValid()) {
            throw new \Exception("Invalid file upload");
        }
    }

    private function generateSimulatedResults(string $documentType): array
    {
        switch ($documentType) {
            case 'nic':
                return [
                    'nic_number' => '199' . rand(0, 9) . rand(100000, 999999) . (rand(0, 1) ? 'V' : 'X'),
                    'full_name' => 'DEMO USER SILVA',
                    'date_of_birth' => '199' . rand(0, 9) . '-' . str_pad(rand(1, 12), 2, '0', STR_PAD_LEFT) . '-' . str_pad(rand(1, 28), 2, '0', STR_PAD_LEFT),
                    'address' => '123 Demo Street, Colombo ' . str_pad(rand(1, 15), 2, '0', STR_PAD_LEFT),
                    'gender' => rand(0, 1) ? 'Male' : 'Female',
                ];

            case 'driving_license':
                return [
                    'license_number' => 'B' . rand(1000000, 9999999),
                    'full_name' => 'DEMO USER PERERA',
                    'expiry_date' => (date('Y') + rand(1, 5)) . '-' . str_pad(rand(1, 12), 2, '0', STR_PAD_LEFT) . '-' . str_pad(rand(1, 28), 2, '0', STR_PAD_LEFT),
                    'vehicle_classes' => ['B1', 'A1'],
                    'issued_date' => (date('Y') - rand(1, 5)) . '-' . str_pad(rand(1, 12), 2, '0', STR_PAD_LEFT) . '-' . str_pad(rand(1, 28), 2, '0', STR_PAD_LEFT),
                ];

            case 'vehicle_registration':
                $provinces = ['WP', 'CP', 'SP', 'NP', 'EP', 'NC', 'NW', 'UVA', 'SAB'];
                $province = $provinces[array_rand($provinces)];
                $letters = chr(rand(65, 90)) . chr(rand(65, 90));
                $numbers = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

                return [
                    'registration_number' => $province . '-' . $letters . '-' . $numbers,
                    'vehicle_make' => ['Toyota', 'Honda', 'Nissan', 'Suzuki', 'Mitsubishi'][rand(0, 4)],
                    'vehicle_model' => ['Corolla', 'Civic', 'Sunny', 'Swift', 'Lancer'][rand(0, 4)],
                    'year' => (date('Y') - rand(0, 15)),
                    'engine_number' => 'ENG' . rand(100000, 999999),
                    'chassis_number' => 'CHS' . rand(100000, 999999),
                ];

            default:
                return [
                    'text_content' => 'Demo extracted text content from the document',
                    'confidence' => rand(80, 95) / 100,
                    'document_type' => $documentType,
                ];
        }
    }

    public function checkLimit(Request $request)
    {
        $clientIp = $request->ip();
        $dailyLimit = config('app.demo_daily_limit', 1);
        $todayCount = DemoRequest::byIp($clientIp)->today()->count();

        return response()->json([
            'success' => true,
            'data' => [
                'limit_reached' => $todayCount >= $dailyLimit,
                'daily_limit' => $dailyLimit,
                'today_count' => $todayCount,
                'remaining' => max(0, $dailyLimit - $todayCount),
                'reset_time' => now()->endOfDay()->toISOString(),
            ],
        ]);
    }

    public function stats()
    {
        // Admin-only stats page
        if (!auth()->user() || !auth()->user()->isAdmin()) {
            abort(403);
        }

        $stats = [
            'total_requests' => DemoRequest::count(),
            'today_requests' => DemoRequest::today()->count(),
            'completed_requests' => DemoRequest::where('status', 'completed')->count(),
            'failed_requests' => DemoRequest::where('status', 'failed')->count(),
            'unique_ips' => DemoRequest::distinct('ip_address')->count('ip_address'),
            'by_document_type' => DemoRequest::selectRaw('document_type, COUNT(*) as count')
                ->groupBy('document_type')
                ->get(),
        ];

        $recentRequests = DemoRequest::orderBy('created_at', 'desc')
            ->limit(50)
            ->get();

        return view('admin.demo-stats', compact('stats', 'recentRequests'));
    }
}
